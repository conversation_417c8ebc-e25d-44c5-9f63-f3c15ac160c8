#!/usr/bin/env node

import { getCSRFData, encryptTimers, getResiTracking } from "./function.js";

// Enhanced display function for shipping information
function displayShippingInfo(data) {
  console.log("📦 SHIPPING TRACKING INFORMATION");
  console.log("═".repeat(50));
  
  // Basic Information
  console.log("\n📋 BASIC INFORMATION:");
  console.log("─".repeat(25));
  console.log(`📍 Tracking Number: ${data.noResi || 'N/A'}`);
  console.log(`📅 Shipment Date: ${data.tanggalKirim || 'N/A'}`);
  console.log(`🚚 Courier: ${data.expedisi || 'N/A'}`);
  console.log(`📊 Status: ${getStatusWithIcon(data.status)}`);
  
  // Shipper Information
  console.log("\n📤 SHIPPER INFORMATION:");
  console.log("─".repeat(25));
  const shipperInfo = parseShipperInfo(data.pengirim);
  console.log(`👤 Name: ${shipperInfo.name}`);
  console.log(`📍 Address: ${shipperInfo.address}`);
  
  // Receiver Information
  console.log("\n📥 RECEIVER INFORMATION:");
  console.log("─".repeat(25));
  const receiverInfo = parseReceiverInfo(data.tujuan, data.penerima);
  console.log(`👤 Name: ${receiverInfo.name}`);
  console.log(`📍 Address: ${receiverInfo.address}`);
  console.log(`📋 Delivery Status: ${receiverInfo.deliveryStatus}`);
  
  // Tracking History
  if (data.perjalanan && data.perjalanan.length > 0) {
    console.log("\n🛣️  TRACKING HISTORY:");
    console.log("─".repeat(25));
    data.perjalanan.forEach((item, index) => {
      const icon = getTrackingIcon(item.keterangan, index, data.perjalanan.length);
      console.log(`${icon} ${item.tanggal} - ${item.keterangan}`);
    });
  }
  
  console.log("\n" + "═".repeat(50));
}

// Helper function to get status with appropriate icon
function getStatusWithIcon(status) {
  if (!status) return "❓ Unknown";
  
  const statusLower = status.toLowerCase();
  if (statusLower.includes('delivered')) return `✅ ${status}`;
  if (statusLower.includes('transit') || statusLower.includes('process')) return `🚛 ${status}`;
  if (statusLower.includes('picked') || statusLower.includes('received')) return `📦 ${status}`;
  if (statusLower.includes('pending') || statusLower.includes('waiting')) return `⏳ ${status}`;
  if (statusLower.includes('failed') || statusLower.includes('error')) return `❌ ${status}`;
  
  return `📋 ${status}`;
}

// Helper function to parse shipper information
function parseShipperInfo(pengirim) {
  if (!pengirim) return { name: 'N/A', address: 'N/A' };
  
  // Clean up the address formatting
  let cleanAddress = pengirim.replace(/\s+/g, ' ').trim();
  
  // Try to extract name and address
  if (cleanAddress.includes('WH ')) {
    const parts = cleanAddress.split('WH ');
    return {
      name: 'WH ' + (parts[1] ? parts[1].split(' ')[0] : 'WAREHOUSE'),
      address: parts[1] ? parts[1].substring(parts[1].indexOf(' ') + 1) : 'N/A'
    };
  }
  
  // If it's all uppercase and long, it's likely an address
  if (cleanAddress.length > 20 && cleanAddress === cleanAddress.toUpperCase()) {
    return {
      name: 'WAREHOUSE',
      address: cleanAddress
    };
  }
  
  return {
    name: cleanAddress,
    address: 'N/A'
  };
}

// Helper function to parse receiver information
function parseReceiverInfo(tujuan, penerima) {
  let name = 'N/A';
  let address = 'N/A';
  let deliveryStatus = 'N/A';
  
  // Extract name and address from tujuan
  if (tujuan) {
    // Clean up spacing
    let cleanTujuan = tujuan.replace(/\s+/g, ' ').trim();
    
    // Try to find where the name ends and address begins
    const words = cleanTujuan.split(' ');
    if (words.length >= 2) {
      // Usually first 2-3 words are name, rest is address
      name = words.slice(0, 2).join(' ');
      address = words.slice(2).join(' ');
    } else {
      address = cleanTujuan;
    }
  }
  
  // Extract delivery status from penerima
  if (penerima) {
    if (penerima.includes('DELIVERED TO')) {
      const match = penerima.match(/DELIVERED TO \[([^\]]+)\]/);
      if (match) {
        const deliveryInfo = match[1].split(' | ');
        if (deliveryInfo.length >= 3) {
          name = deliveryInfo[0].trim();
          deliveryStatus = `✅ Delivered on ${deliveryInfo[1]} at ${deliveryInfo[2]}`;
        }
      }
    } else {
      deliveryStatus = penerima;
    }
  }
  
  return { name, address, deliveryStatus };
}

// Helper function to get appropriate icon for tracking steps
function getTrackingIcon(keterangan, index, total) {
  const step = keterangan.toLowerCase();
  
  if (step.includes('picked up') || step.includes('shipment picked')) return '📦';
  if (step.includes('received at') && step.includes('sorting')) return '🏭';
  if (step.includes('received at') && !step.includes('sorting')) return '📍';
  if (step.includes('processed at') || step.includes('process and forward')) return '⚙️';
  if (step.includes('with delivery courier') || step.includes('out for delivery')) return '🚛';
  if (step.includes('delivered to')) return '✅';
  if (step.includes('transit') || step.includes('forward')) return '🚚';
  
  // Default icons based on position
  if (index === 0) return '🏁'; // Start
  if (index === total - 1) return '🎯'; // End
  
  return '📍'; // Default
}

// Main tracking function (clean version without debug info)
async function trackResi(resiNumber, expedition = "JNE") {
  try {
    console.log("Getting tracking data...");

    const csrf = await getCSRFData(resiNumber, expedition);
    const timers = await encryptTimers(resiNumber);
    const trackingData = await getResiTracking(resiNumber, csrf, timers, expedition);
    
    if (trackingData.valid) {
      console.log("✅ Tracking data retrieved successfully!");
      console.log("");
      displayShippingInfo(trackingData.data);
    } else {
      console.log("❌ TRACKING FAILED");
      console.log("═".repeat(30));
      console.log(`📋 Message: ${trackingData.message || 'Package not found or invalid tracking number'}`);
      console.log(`📦 Tracking Number: ${resiNumber}`);
      console.log(`🚚 Expedition: ${expedition}`);
      console.log("\n💡 Please check:");
      console.log("   • Tracking number is correct");
      console.log("   • Expedition code is correct (JNE, JET, TIKI, etc.)");
      console.log("   • Package has been processed by the courier");
    }
    
  } catch (error) {
    console.log("❌ ERROR OCCURRED");
    console.log("═".repeat(30));
    console.log(`📋 Error: ${error.message}`);
    console.log("\n💡 This might be due to:");
    console.log("   • Network connectivity issues");
    console.log("   • Courier website is down");
    console.log("   • Invalid tracking number format");
  }
}

// Get command line arguments
const resiNumber = process.argv[2];
const expedition = process.argv[3] || "JNE";

if (!resiNumber) {
  console.log("📦 RESI TRACKING TOOL");
  console.log("═".repeat(30));
  console.log("❌ Please provide a tracking number");
  console.log("\n📋 Usage:");
  console.log("   node track-resi.js <tracking_number> [expedition]");
  console.log("\n📝 Examples:");
  console.log("   node track-resi.js CSS0925755453307 JNE");
  console.log("   node track-resi.js JET123456789 JET");
  console.log("   node track-resi.js TIKI123456789 TIKI");
  console.log("\n🚚 Available expeditions:");
  console.log("   JNE, JET, TIKI, POS, SICEPAT, ANTERAJA, etc.");
  process.exit(1);
}

// Run the tracking
trackResi(resiNumber, expedition);
