# PowerShell Resi Tracking Script
# Usage: .\track-resi.ps1 <resi_number> [expedition]
# Example: .\track-resi.ps1 CSS0925755453307 JNE

param(
    [Parameter(Mandatory=$true)]
    [string]$ResiNumber,
    
    [Parameter(Mandatory=$false)]
    [string]$Expedition = "JNE"
)

# Function to display colored text
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# Function to clean HTML text
function Clean-Text {
    param([string]$Text)
    $Text = $Text -replace '<[^>]*>', ''
    $Text = $Text -replace '&nbsp;', ' '
    $Text = $Text -replace '&amp;', '&'
    $Text = $Text -replace '\s+', ' '
    return $Text.Trim()
}

# Function to get status icon
function Get-StatusIcon {
    param([string]$Status)
    $StatusLower = $Status.ToLower()
    if ($StatusLower -like "*delivered*") { return "✅" }
    elseif ($StatusLower -like "*transit*" -or $StatusLower -like "*process*") { return "🚛" }
    elseif ($StatusLower -like "*picked*" -or $StatusLower -like "*received*") { return "📦" }
    else { return "📋" }
}

# Function to get tracking icon
function Get-TrackingIcon {
    param(
        [string]$Description,
        [int]$Index,
        [int]$Total
    )
    $DescLower = $Description.ToLower()
    if ($DescLower -like "*picked up*") { return "📦" }
    elseif ($DescLower -like "*sorting*") { return "🏭" }
    elseif ($DescLower -like "*received*") { return "📍" }
    elseif ($DescLower -like "*process*") { return "⚙️" }
    elseif ($DescLower -like "*courier*") { return "🚛" }
    elseif ($DescLower -like "*delivered*") { return "✅" }
    elseif ($Index -eq 0) { return "🏁" }
    elseif ($Index -eq ($Total - 1)) { return "🎯" }
    else { return "📍" }
}

# Function to parse shipper info
function Parse-ShipperInfo {
    param([string]$Pengirim)
    $Clean = Clean-Text $Pengirim
    if ($Clean -like "*WH *") {
        return @{
            Name = "WAREHOUSE"
            Address = $Clean -replace "WH ", ""
        }
    }
    return @{
        Name = if ($Clean) { $Clean } else { "N/A" }
        Address = "N/A"
    }
}

# Function to parse receiver info
function Parse-ReceiverInfo {
    param([string]$Tujuan)
    $Clean = Clean-Text $Tujuan
    $Words = $Clean -split '\s+'
    return @{
        Name = if ($Words.Length -ge 2) { "$($Words[0]) $($Words[1])" } else { "N/A" }
        Address = if ($Words.Length -gt 2) { ($Words[2..($Words.Length-1)] -join " ") } else { "N/A" }
    }
}

# Main tracking function
function Track-Resi {
    param(
        [string]$ResiNumber,
        [string]$Expedition
    )
    
    try {
        Write-ColorText "Getting tracking data..." "Blue"
        
        # Step 1: Get CSRF tokens
        $csrfUrl = "https://cekresi.com/?noresi=$ResiNumber&e=$Expedition"
        $headers = @{
            'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        $csrfResponse = Invoke-RestMethod -Uri $csrfUrl -Headers $headers -Method Get
        
        # Extract CSRF tokens using regex
        $viewstateMatch = [regex]::Match($csrfResponse, 'name="viewstate"\s+value="([^"]*)"')
        $secretKeyMatch = [regex]::Match($csrfResponse, 'name="secret_key"\s+value="([^"]*)"')
        
        if (-not $viewstateMatch.Success -or -not $secretKeyMatch.Success) {
            throw "Failed to get CSRF tokens"
        }
        
        $viewstate = $viewstateMatch.Groups[1].Value
        $secretKey = $secretKeyMatch.Groups[1].Value
        $timers = "kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
        
        # Step 2: Get tracking data
        $trackingUrl = "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f"
        $trackingHeaders = @{
            'Content-Type' = 'application/x-www-form-urlencoded; charset=UTF-8'
            'Origin' = 'https://cekresi.com'
            'Referer' = 'https://cekresi.com/'
            'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        $body = "viewstate=$viewstate&secret_key=$secretKey&e=$Expedition&noresi=$ResiNumber&timers=$timers"
        
        $trackingResponse = Invoke-RestMethod -Uri $trackingUrl -Method Post -Headers $trackingHeaders -Body $body
        
        if ($trackingResponse -like "*alert-success*") {
            Write-ColorText "✅ Tracking data retrieved successfully!" "Green"
            Write-Host ""
            Display-TrackingInfo $trackingResponse $ResiNumber
        } else {
            Write-ColorText "❌ No tracking data found" "Red"
            Write-Host "📦 Tracking Number: $ResiNumber"
            Write-Host "🚚 Expedition: $Expedition"
        }
        
    } catch {
        Write-ColorText "❌ ERROR OCCURRED" "Red"
        Write-Host "📋 Error: $($_.Exception.Message)"
    }
}

# Function to display tracking information
function Display-TrackingInfo {
    param(
        [string]$Html,
        [string]$ResiNumber
    )
    
    Write-ColorText "📦 SHIPPING TRACKING INFORMATION" "Cyan"
    Write-Host "══════════════════════════════════════════════════"
    
    # Extract basic information using regex
    $expedisiMatch = [regex]::Match($Html, '<strong>([^<]*Express[^<]*)</strong>')
    $statusMatch = [regex]::Match($Html, 'id="status_resi"[^>]*>([^<]*)')
    $pengirimMatch = [regex]::Match($Html, 'Dikirim oleh</td>[\s\S]*?<td[^>]*>[\s\S]*?<td[^>]*>([^<]*)')
    $tujuanMatch = [regex]::Match($Html, 'Dikirim ke</td>[\s\S]*?<td[^>]*>[\s\S]*?<td[^>]*>([^<]*)')
    $tanggalMatch = [regex]::Match($Html, 'Dikirim tanggal</td>[\s\S]*?<td[^>]*>[\s\S]*?<td[^>]*>([^<]*)')
    
    $expedisi = if ($expedisiMatch.Success) { Clean-Text $expedisiMatch.Groups[1].Value } else { "N/A" }
    $status = if ($statusMatch.Success) { Clean-Text $statusMatch.Groups[1].Value } else { "N/A" }
    $pengirim = if ($pengirimMatch.Success) { Clean-Text $pengirimMatch.Groups[1].Value } else { "N/A" }
    $tujuan = if ($tujuanMatch.Success) { Clean-Text $tujuanMatch.Groups[1].Value } else { "N/A" }
    $tanggalKirim = if ($tanggalMatch.Success) { Clean-Text $tanggalMatch.Groups[1].Value } else { "N/A" }
    
    # Basic Information
    Write-Host ""
    Write-ColorText "📋 BASIC INFORMATION:" "Yellow"
    Write-Host "─────────────────────────"
    Write-Host "📍 Tracking Number: $ResiNumber"
    Write-Host "📅 Shipment Date: $tanggalKirim"
    Write-Host "🚚 Courier: $expedisi"
    Write-Host "📊 Status: $(Get-StatusIcon $status) $status"
    
    # Shipper Information
    Write-Host ""
    Write-ColorText "📤 SHIPPER INFORMATION:" "Yellow"
    Write-Host "─────────────────────────"
    $shipperInfo = Parse-ShipperInfo $pengirim
    Write-Host "👤 Name: $($shipperInfo.Name)"
    Write-Host "📍 Address: $($shipperInfo.Address)"
    
    # Receiver Information
    Write-Host ""
    Write-ColorText "📥 RECEIVER INFORMATION:" "Yellow"
    Write-Host "─────────────────────────"
    $receiverInfo = Parse-ReceiverInfo $tujuan
    Write-Host "👤 Name: $($receiverInfo.Name)"
    Write-Host "📍 Address: $($receiverInfo.Address)"
    
    # Tracking History
    Write-Host ""
    Write-ColorText "🛣️  TRACKING HISTORY:" "Yellow"
    Write-Host "─────────────────────────"
    
    # Extract tracking history
    $tableMatches = [regex]::Matches($Html, '<tr[^>]*>[\s\S]*?</tr>')
    $trackingHistory = @()
    
    foreach ($match in $tableMatches) {
        $cellMatches = [regex]::Matches($match.Value, '<td[^>]*>([\s\S]*?)</td>')
        if ($cellMatches.Count -ge 2) {
            $date = Clean-Text $cellMatches[0].Groups[1].Value
            $desc = Clean-Text $cellMatches[1].Groups[1].Value
            
            if ($date -and $desc -and $date -notlike "*Tanggal*" -and $date.Length -gt 5) {
                $trackingHistory += @{
                    Date = $date
                    Description = $desc
                }
            }
        }
    }
    
    for ($i = 0; $i -lt $trackingHistory.Count; $i++) {
        $item = $trackingHistory[$i]
        $icon = Get-TrackingIcon $item.Description $i $trackingHistory.Count
        Write-Host "$icon $($item.Date) - $($item.Description)"
    }
    
    Write-Host ""
    Write-Host "══════════════════════════════════════════════════"
}

# Main execution
if (-not $ResiNumber) {
    Write-ColorText "📦 RESI TRACKING TOOL" "Cyan"
    Write-Host "═══════════════════════════════════"
    Write-ColorText "❌ Please provide a tracking number" "Red"
    Write-Host ""
    Write-ColorText "📋 Usage:" "Yellow"
    Write-Host "   .\track-resi.ps1 <tracking_number> [expedition]"
    Write-Host ""
    Write-ColorText "📝 Examples:" "Yellow"
    Write-Host "   .\track-resi.ps1 CSS0925755453307 JNE"
    Write-Host "   .\track-resi.ps1 JET123456789 JET"
    exit 1
}

Track-Resi $ResiNumber $Expedition
