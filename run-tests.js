#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  const args = process.argv.slice(2);
  const resiNumber = args[0];
  const testType = args[1] || 'both'; // 'function', 'api', or 'both'
  
  if (!resiNumber) {
    colorLog('red', '❌ Please provide a resi number as argument');
    colorLog('yellow', 'Usage: node run-tests.js <resi_number> [test_type]');
    colorLog('yellow', 'Test types: function, api, both (default)');
    colorLog('yellow', 'Example: node run-tests.js JET123456789');
    colorLog('yellow', 'Example: node run-tests.js JET123456789 function');
    colorLog('yellow', 'Example: node run-tests.js JET123456789 api');
    process.exit(1);
  }
  
  colorLog('cyan', '🚀 Starting Resi Tracking Tests');
  colorLog('cyan', '=' .repeat(50));
  colorLog('blue', `📦 Resi Number: ${resiNumber}`);
  colorLog('blue', `🧪 Test Type: ${testType}`);
  console.log();
  
  try {
    if (testType === 'function' || testType === 'both') {
      colorLog('magenta', '🔧 Running Direct Function Test...');
      colorLog('magenta', '=' .repeat(40));
      await runCommand('node', ['test-tracking.js', resiNumber]);
      console.log();
    }
    
    if (testType === 'api' || testType === 'both') {
      colorLog('magenta', '🌐 Running API Test...');
      colorLog('magenta', '=' .repeat(40));
      colorLog('yellow', '⚠️  Make sure the server is running with: npm start');
      console.log();
      await runCommand('node', ['test-api.js', resiNumber]);
      console.log();
    }
    
    colorLog('green', '✅ All tests completed successfully!');
    
  } catch (error) {
    colorLog('red', `❌ Test failed: ${error.message}`);
    process.exit(1);
  }
}

main();
