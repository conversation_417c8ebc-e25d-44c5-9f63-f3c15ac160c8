@echo off
echo 🐍 PYTHON RESI TRACKER SETUP
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed!
    echo.
    echo 💡 Please install Python from: https://python.org/
    echo    Download Python 3.8+ and make sure to check "Add to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed
python --version

REM Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip is not available!
    pause
    exit /b 1
)

echo ✅ pip is available
pip --version
echo.

REM Install dependencies
echo 📥 Installing Python dependencies...
pip install -r requirements_simple.txt

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies!
    echo.
    echo 💡 Try running as administrator or use:
    echo    pip install --user requests beautifulsoup4
    pause
    exit /b 1
)

echo.
echo ✅ Setup completed successfully!
echo.
echo 📋 Usage:
echo    python track_resi_simple.py ^<resi_number^> [expedition]
echo.
echo 📝 Example:
echo    python track_resi_simple.py CSS0925755453307 JNE
echo.
echo 🚚 Available expeditions: JNE, JET, TIKI, POS, etc.
echo.
pause
