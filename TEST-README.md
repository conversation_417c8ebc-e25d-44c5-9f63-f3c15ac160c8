# Resi Tracking Test Scripts

This directory contains test scripts to manually run and verify the resi tracking functionality.

## Available Test Scripts

### 1. `test-tracking.js` - Direct Function Test
Tests the core tracking functions directly without going through the API.

**Usage:**
```bash
node test-tracking.js <resi_number>
```

**Example:**
```bash
node test-tracking.js JET123456789
```

**What it does:**
- Tests `getCSRFData()` function
- Tests `encryptTimers()` function  
- Tests `getResiTracking()` function
- Shows detailed step-by-step execution
- Displays complete tracking information

### 2. `test-api.js` - API Endpoint Test
Tests the HTTP API endpoint to ensure the server is working correctly.

**Usage:**
```bash
node test-api.js <resi_number> [base_url]
```

**Examples:**
```bash
node test-api.js JET123456789
node test-api.js JET123456789 http://localhost:3000
```

**What it does:**
- Checks if server is running
- Makes HTTP request to `/cek-resi/:number` endpoint
- Shows API response
- Handles network errors gracefully

### 3. `run-tests.js` - Comprehensive Test Runner
Runs both function and API tests with colored output.

**Usage:**
```bash
node run-tests.js <resi_number> [test_type]
```

**Test Types:**
- `function` - Run only direct function tests
- `api` - Run only API tests  
- `both` - Run both tests (default)

**Examples:**
```bash
node run-tests.js JET123456789
node run-tests.js JET123456789 function
node run-tests.js JET123456789 api
```

## How to Run Tests

### Prerequisites
1. Make sure all dependencies are installed:
   ```bash
   npm install
   ```

### Running Direct Function Tests
```bash
# Test the core functions directly
node test-tracking.js JET123456789
```

### Running API Tests
1. Start the server in one terminal:
   ```bash
   npm start
   ```

2. In another terminal, run the API test:
   ```bash
   node test-api.js JET123456789
   ```

### Running All Tests
```bash
# Make sure server is running first
npm start

# In another terminal
node run-tests.js JET123456789
```

## Sample Resi Numbers for Testing

You can use these sample resi numbers for testing (replace with real ones):
- `JET123456789`
- `JNE123456789`
- `TIKI123456789`

## Expected Output

### Successful Function Test:
```
🔍 Testing tracking for resi number: JET123456789
==================================================
📋 Step 1: Getting CSRF data...
✅ CSRF data obtained:
   - viewstate: MTIzNDU2Nzg5...
   - secret_key: YWJjZGVmZ2hpams...

🔐 Step 2: Encrypting timers...
✅ Timers encrypted:
   - encrypted value: U2FsdGVkX1%2B8xvzJ3K...

📦 Step 3: Getting tracking data...
✅ Tracking data retrieved successfully!

📋 TRACKING INFORMATION:
==============================
Expedisi: J&T Express
No Resi: JET123456789
Pengirim: Jakarta
Tujuan: Bandung
Status: Delivered
Tanggal Kirim: 2024-01-15
Penerima: John Doe

🛣️  PERJALANAN PAKET:
==============================
1. 2024-01-15 - Package picked up
2. 2024-01-16 - In transit to destination
3. 2024-01-17 - Package delivered
```

### Successful API Test:
```
🏥 Testing server health: http://localhost:3000
========================================
✅ Server is running!
Response: {
  "status": 200,
  "author": "Romi Muharom",
  "message": "Selamat datang di API Cek Resi Indonesia, endpoint ada di /cek-resi/:noresi"
}

🌐 Testing API endpoint: http://localhost:3000/cek-resi/JET123456789
============================================================
✅ API Response received!
Status Code: 200
Response Data: {
  "status": 200,
  "data": {
    "valid": true,
    "data": {
      "expedisi": "J&T Express",
      "noResi": "JET123456789",
      ...
    }
  }
}
```

## Troubleshooting

### Common Issues:

1. **"Server is not responding"**
   - Make sure to start the server with `npm start`
   - Check if port 3000 is available

2. **"CSRF data is null"**
   - The resi number might be invalid
   - The external API might be down
   - Network connectivity issues

3. **"Tracking data not found"**
   - The resi number doesn't exist
   - The package hasn't been processed yet
   - Wrong courier/expedisi

4. **Module import errors**
   - Make sure you're using Node.js version that supports ES modules
   - Check that `"type": "module"` is in package.json

### Debug Mode
Add more detailed logging by modifying the test scripts or check the server logs when running `npm start`.
