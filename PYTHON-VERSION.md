# 🐍 Python Version of Resi Tracker

I've successfully converted your Node.js resi tracking app to Python! Here are the available versions:

## 📁 **Python Files Created:**

### **Main Python Scripts:**
1. ✅ `track_resi_working.py` - Most complete version with exact headers
2. ✅ `track_resi_simple.py` - Simplified version with minimal dependencies
3. ✅ `track_resi.py` - Full-featured version (with crypto dependencies)

### **Setup Files:**
4. ✅ `requirements.txt` - Full dependencies
5. ✅ `requirements_simple.txt` - Minimal dependencies
6. ✅ `setup_python.bat` - Windows setup script
7. ✅ `setup_python.sh` - Linux/Mac setup script

## 🚀 **Quick Start (Recommended)**

### **Option 1: Simple Setup**
```bash
# Install dependencies
pip install requests beautifulsoup4

# Run the tracker
python track_resi_working.py CSS0925755453307 JNE
```

### **Option 2: Automated Setup**
```bash
# Windows
setup_python.bat

# Linux/Mac
chmod +x setup_python.sh
./setup_python.sh
```

## 📦 **Dependencies Comparison**

### **Minimal Version** (`track_resi_simple.py`):
```
requests>=2.28.0
beautifulsoup4>=4.11.0
```

### **Full Version** (`track_resi.py`):
```
requests>=2.28.0
beautifulsoup4>=4.11.0
pycryptodome>=3.15.0
lxml>=4.9.0
```

## 🎯 **Usage Examples**

All Python versions use the same command format:

```bash
# JNE tracking
python track_resi_working.py CSS0925755453307 JNE

# J&T Express tracking  
python track_resi_working.py JET123456789 JET

# TIKI tracking
python track_resi_working.py TIKI123456789 TIKI

# Default to JNE if no expedition specified
python track_resi_working.py CSS0925755453307
```

## 🔧 **Deployment for Another PC**

### **Files to Copy:**
```
📄 track_resi_working.py      - Main Python script
📄 requirements_simple.txt    - Dependencies
📄 setup_python.bat          - Windows setup (optional)
📄 setup_python.sh           - Linux/Mac setup (optional)
```

### **Setup on New PC:**
1. **Install Python 3.8+** from https://python.org/
2. **Copy the files** to a folder
3. **Install dependencies:**
   ```bash
   pip install -r requirements_simple.txt
   ```
4. **Run the tracker:**
   ```bash
   python track_resi_working.py CSS0925755453307 JNE
   ```

## 🎨 **Expected Output**

The Python version produces the same beautiful output as the Node.js version:

```
Getting tracking data...
✅ Tracking data retrieved successfully!

📦 SHIPPING TRACKING INFORMATION
══════════════════════════════════════════════════

📋 BASIC INFORMATION:
─────────────────────────
📍 Tracking Number: CSS0925755453307
📅 Shipment Date: 2025-06-03
🚚 Courier: JNE Express
📊 Status: ✅ Delivered

📤 SHIPPER INFORMATION:
─────────────────────────
👤 Name: WAREHOUSE
📍 Address: DHL PALEMBANG

📥 RECEIVER INFORMATION:
─────────────────────────
👤 Name: SUSAN APRIANSYAH
📍 Address: CURUP TIMUR,REJANG LEBONG

🛣️  TRACKING HISTORY:
─────────────────────────
📦 03-06-2025 13:37 - SHIPMENT PICKED UP BY JNE COURIER
📍 03-06-2025 18:05 - SHIPMENT RECEIVED AT [PALEMBANG]
🏭 03-06-2025 22:48 - RECEIVED AT SORTING CENTER
⚙️ 03-06-2025 22:53 - PROCESSED AT SORTING CENTER
⚙️ 05-06-2025 16:43 - PROCESS AND FORWARD TO DESTINATION
📍 05-06-2025 16:43 - RECEIVED AT WAREHOUSE
📍 06-06-2025 09:34 - RECEIVED AT DESTINATION
🚛 06-06-2025 10:40 - WITH DELIVERY COURIER
✅ 06-06-2025 14:29 - DELIVERED TO RECIPIENT

══════════════════════════════════════════════════
```

## 🔄 **Converting to Executable**

### **Using PyInstaller:**
```bash
# Install PyInstaller
pip install pyinstaller

# Create executable
pyinstaller --onefile --name track-resi track_resi_working.py

# The executable will be in dist/track-resi.exe (Windows) or dist/track-resi (Linux/Mac)
```

### **Using the executable:**
```bash
# Windows
track-resi.exe CSS0925755453307 JNE

# Linux/Mac
./track-resi CSS0925755453307 JNE
```

## 🆚 **Python vs Node.js Comparison**

| Feature | Node.js Version | Python Version |
|---------|----------------|----------------|
| **Dependencies** | npm install | pip install |
| **File Size** | ~4 files | ~2 files |
| **Portability** | Requires Node.js | Requires Python |
| **Performance** | Fast | Fast |
| **Executable** | pkg | PyInstaller |
| **Cross-platform** | ✅ | ✅ |
| **Ease of Setup** | ✅ | ✅ |

## 💡 **Advantages of Python Version**

### **Pros:**
- ✅ **Widely Available**: Python often pre-installed on Linux/Mac
- ✅ **Simple Dependencies**: Only 2 packages needed
- ✅ **Easy to Read**: Python syntax is very readable
- ✅ **Great for Scripting**: Perfect for automation
- ✅ **Cross-platform**: Works on Windows, Linux, Mac
- ✅ **Executable Creation**: Easy with PyInstaller

### **Cons:**
- ❌ **Slightly Slower**: Than Node.js for HTTP requests
- ❌ **Encoding Issues**: Windows console encoding can be tricky
- ❌ **SSL Issues**: May need to disable SSL verification

## 🔧 **Troubleshooting**

### **Common Issues:**

#### **1. "python is not recognized"**
**Solution:** Install Python from https://python.org/ and check "Add to PATH"

#### **2. "No module named 'requests'"**
**Solution:** 
```bash
pip install requests beautifulsoup4
```

#### **3. Unicode/Encoding Errors (Windows)**
**Solution:** The script includes automatic encoding fixes

#### **4. SSL Certificate Errors**
**Solution:** The script disables SSL verification for compatibility

#### **5. "No tracking data found"**
**Solution:** 
- Check internet connection
- Verify tracking number is correct
- Try the Node.js version if Python version fails

## 🎯 **Recommendation**

### **For Most Users:**
**Stick with the Node.js version** (`test-tracking.js`) as it's proven to work perfectly.

### **For Python Enthusiasts:**
Use `track_resi_working.py` - it has the most complete implementation.

### **For Distribution:**
Create executables with PyInstaller for easy sharing without requiring Python installation.

### **For Integration:**
The Python version is great for integrating into larger Python projects or automation scripts.

## 📞 **Support**

If the Python version doesn't work as expected:
1. Try the Node.js version first (it's proven to work)
2. Check your Python version (3.8+ recommended)
3. Verify all dependencies are installed
4. Test with the known working resi: `CSS0925755453307`

The Python conversion maintains the same functionality and output format as your original Node.js version! 🎉
