# Simple Curl Commands for Resi Tracking

## Quick One-Liner (Windows)

Replace `CSS0925755453307` with your resi number and `JNE` with your expedition:

```cmd
curl "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" -H "Accept: */*" -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" -H "Connection: keep-alive" -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" -H "Origin: https://cekresi.com" -H "Referer: https://cekresi.com/" -H "Sec-Fetch-Dest: empty" -H "Sec-Fetch-Mode: cors" -H "Sec-Fetch-Site: same-site" -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36 Edg/*********" -H "sec-ch-ua: \"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"" -H "sec-ch-ua-mobile: ?0" -H "sec-ch-ua-platform: \"Windows\"" --data-raw "viewstate=5803694&secret_key=d5c7a62068deec875d2aa4fcbb4128c0&e=JNE&noresi=CSS0925755453307&timers=kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
```

## Step-by-Step Process

### Step 1: Get CSRF Tokens
First, get the viewstate and secret_key from cekresi.com:

```bash
curl "https://cekresi.com/?noresi=CSS0925755453307&e=JNE" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
  -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8" \
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" \
  -H "Connection: keep-alive"
```

Look for these values in the HTML response:
- `<input name="viewstate" value="XXXXXXX">`
- `<input name="secret_key" value="XXXXXXX">`

### Step 2: Get Tracking Data
Use the extracted values in the tracking API call:

```bash
curl "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" \
  -H "Accept: */*" \
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" \
  -H "Connection: keep-alive" \
  -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
  -H "Origin: https://cekresi.com" \
  -H "Referer: https://cekresi.com/" \
  -H "Sec-Fetch-Dest: empty" \
  -H "Sec-Fetch-Mode: cors" \
  -H "Sec-Fetch-Site: same-site" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
  -H "sec-ch-ua: \"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "sec-ch-ua-platform: \"Windows\"" \
  --data-raw "viewstate=YOUR_VIEWSTATE&secret_key=YOUR_SECRET_KEY&e=JNE&noresi=CSS0925755453307&timers=kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
```

## Working Example

Here's a complete working example with the values from your successful test:

```bash
# Step 1: Get CSRF (you can skip this if you use the known values)
curl "https://cekresi.com/?noresi=CSS0925755453307&e=JNE" -s | grep -E "(viewstate|secret_key)" | head -2

# Step 2: Track with known values
curl "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" \
  -H "Accept: */*" \
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" \
  -H "Connection: keep-alive" \
  -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
  -H "Origin: https://cekresi.com" \
  -H "Referer: https://cekresi.com/" \
  -H "Sec-Fetch-Dest: empty" \
  -H "Sec-Fetch-Mode: cors" \
  -H "Sec-Fetch-Site: same-site" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
  -H "sec-ch-ua: \"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "sec-ch-ua-platform: \"Windows\"" \
  --data-raw "viewstate=5803694&secret_key=d5c7a62068deec875d2aa4fcbb4128c0&e=JNE&noresi=CSS0925755453307&timers=kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
```

## Parameters Explanation

- **viewstate**: CSRF token from the initial page load
- **secret_key**: Another CSRF token from the initial page load  
- **e**: Expedition code (JNE, JET, TIKI, POS, etc.)
- **noresi**: The tracking number
- **timers**: Encrypted timestamp (can reuse the same value for multiple requests)

## Important Notes

1. **CSRF Tokens**: The `viewstate` and `secret_key` values change periodically, so you might need to fetch them fresh for each session.

2. **Timers**: The `timers` parameter is an encrypted timestamp. The value `kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D` seems to work consistently.

3. **Expedition Codes**: Common codes include:
   - JNE: JNE Express
   - JET: J&T Express  
   - TIKI: TIKI
   - POS: Pos Indonesia
   - SICEPAT: SiCepat

4. **Response Format**: The response is HTML that needs to be parsed to extract the tracking information.

## Quick Test

Test with the working resi number:

### Curl (Linux/Mac/Windows with curl)
```bash
curl -k -X POST "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" \
  -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
  -H "Origin: https://cekresi.com" \
  -H "Referer: https://cekresi.com/" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
  -d "viewstate=5803694&secret_key=d5c7a62068deec875d2aa4fcbb4128c0&e=JNE&noresi=CSS0925755453307&timers=kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
```

### PowerShell (Windows)
```powershell
$headers = @{
    'Content-Type' = 'application/x-www-form-urlencoded; charset=UTF-8'
    'Origin' = 'https://cekresi.com'
    'Referer' = 'https://cekresi.com/'
    'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
}

$body = "viewstate=5803694&secret_key=d5c7a62068deec875d2aa4fcbb4128c0&e=JNE&noresi=CSS0925755453307&timers=kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"

$response = Invoke-RestMethod -Uri "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" -Method POST -Headers $headers -Body $body

Write-Output $response
```

### Python (Alternative)
```python
import requests

url = "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f"

headers = {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Origin': 'https://cekresi.com',
    'Referer': 'https://cekresi.com/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
}

data = "viewstate=5803694&secret_key=d5c7a62068deec875d2aa4fcbb4128c0&e=JNE&noresi=CSS0925755453307&timers=kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"

response = requests.post(url, headers=headers, data=data)
print(response.text)
```

This should return the HTML with tracking information that you can parse as needed.

## Note about SSL
If you get SSL errors with curl, add the `-k` flag to ignore SSL certificate verification (not recommended for production use).
