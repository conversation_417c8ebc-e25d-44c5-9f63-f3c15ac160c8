#!/bin/bash

# Simple curl script to track resi without Node.js
# Usage: ./curl-tracking.sh <resi_number> [expedition]
# Example: ./curl-tracking.sh CSS0925755453307 JNE

RESI_NUMBER=$1
EXPEDITION=${2:-JNE}

if [ -z "$RESI_NUMBER" ]; then
    echo "❌ Please provide a resi number"
    echo "Usage: ./curl-tracking.sh <resi_number> [expedition]"
    echo "Example: ./curl-tracking.sh CSS0925755453307 JNE"
    exit 1
fi

echo "🔍 Tracking resi: $RESI_NUMBER with expedition: $EXPEDITION"
echo "=================================================="

# Step 1: Get CSRF tokens from cekresi.com
echo "📋 Step 1: Getting CSRF tokens..."
CSRF_RESPONSE=$(curl -s "https://cekresi.com/?noresi=$RESI_NUMBER&e=$EXPEDITION" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
  -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8" \
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" \
  -H "Connection: keep-alive")

# Extract viewstate and secret_key using grep and sed
VIEWSTATE=$(echo "$CSRF_RESPONSE" | grep -o 'name="viewstate" value="[^"]*"' | sed 's/name="viewstate" value="//;s/"//')
SECRET_KEY=$(echo "$CSRF_RESPONSE" | grep -o 'name="secret_key" value="[^"]*"' | sed 's/name="secret_key" value="//;s/"//')

if [ -z "$VIEWSTATE" ] || [ -z "$SECRET_KEY" ]; then
    echo "❌ Failed to get CSRF tokens"
    echo "Viewstate: $VIEWSTATE"
    echo "Secret Key: $SECRET_KEY"
    exit 1
fi

echo "✅ CSRF tokens obtained"
echo "   - viewstate: ${VIEWSTATE:0:20}..."
echo "   - secret_key: ${SECRET_KEY:0:20}..."

# Step 2: Create encrypted timers (this is a simplified version)
# For a complete solution, you'd need to implement the encryption algorithm
# For now, we'll use a sample encrypted value that works
TIMERS="kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"

echo ""
echo "🔐 Step 2: Using encrypted timers..."
echo "✅ Timers: ${TIMERS:0:30}..."

# Step 3: Get tracking data
echo ""
echo "📦 Step 3: Getting tracking data..."

TRACKING_RESPONSE=$(curl -s "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" \
  -H "Accept: */*" \
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" \
  -H "Connection: keep-alive" \
  -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
  -H "Origin: https://cekresi.com" \
  -H "Referer: https://cekresi.com/" \
  -H "Sec-Fetch-Dest: empty" \
  -H "Sec-Fetch-Mode: cors" \
  -H "Sec-Fetch-Site: same-site" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" \
  -H "sec-ch-ua: \"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "sec-ch-ua-platform: \"Windows\"" \
  --data-raw "viewstate=$VIEWSTATE&secret_key=$SECRET_KEY&e=$EXPEDITION&noresi=$RESI_NUMBER&timers=$TIMERS")

if [ -z "$TRACKING_RESPONSE" ]; then
    echo "❌ Failed to get tracking response"
    exit 1
fi

echo "✅ Tracking response received!"
echo ""
echo "📋 RAW RESPONSE:"
echo "================"
echo "$TRACKING_RESPONSE"
echo ""

# Try to extract some basic info (this is simplified - you might want to use a proper HTML parser)
echo "📦 EXTRACTED INFO (Basic):"
echo "=========================="

# Extract expedition name
EXPEDISI=$(echo "$TRACKING_RESPONSE" | grep -o '<strong>[^<]*Express[^<]*</strong>' | head -1 | sed 's/<[^>]*>//g')
if [ ! -z "$EXPEDISI" ]; then
    echo "Expedisi: $EXPEDISI"
fi

# Extract resi number
NORESI=$(echo "$TRACKING_RESPONSE" | grep -o '<strong>[A-Z0-9]\{10,\}</strong>' | head -1 | sed 's/<[^>]*>//g')
if [ ! -z "$NORESI" ]; then
    echo "No Resi: $NORESI"
fi

# Extract status
STATUS=$(echo "$TRACKING_RESPONSE" | grep -o 'id="status_resi"[^>]*>[^<]*' | sed 's/id="status_resi"[^>]*>//g')
if [ ! -z "$STATUS" ]; then
    echo "Status: $STATUS"
fi

echo ""
echo "💡 For complete parsing, use the Node.js version or implement HTML parsing"
