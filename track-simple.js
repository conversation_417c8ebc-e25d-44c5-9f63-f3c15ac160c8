#!/usr/bin/env node

// Ultra-simple resi tracking - single file with minimal dependencies
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Simple curl-based tracking
async function trackResi(resiNumber, expedition = "JNE") {
  try {
    console.log("Getting tracking data...");
    
    // Use curl to get CSRF tokens
    const csrfCommand = `curl -s "https://cekresi.com/?noresi=${resiNumber}&e=${expedition}" -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"`;
    
    const { stdout: csrfResponse } = await execAsync(csrfCommand);
    
    // Extract CSRF tokens
    const viewstateMatch = csrfResponse.match(/name="viewstate"\s+value="([^"]*)"/);
    const secretKeyMatch = csrfResponse.match(/name="secret_key"\s+value="([^"]*)"/);
    
    if (!viewstateMatch || !secretKeyMatch) {
      throw new Error('Failed to get CSRF tokens');
    }
    
    const viewstate = viewstateMatch[1];
    const secretKey = secretKeyMatch[1];
    const timers = "kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D";
    
    // Get tracking data using curl
    const trackingCommand = `curl -s "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" -H "Origin: https://cekresi.com" -H "Referer: https://cekresi.com/" -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" --data-raw "viewstate=${viewstate}&secret_key=${secretKey}&e=${expedition}&noresi=${resiNumber}&timers=${timers}"`;
    
    const { stdout: trackingResponse } = await execAsync(trackingCommand);
    
    if (trackingResponse && trackingResponse.includes('alert-success')) {
      console.log("✅ Tracking data retrieved successfully!");
      console.log("");
      parseAndDisplayTracking(trackingResponse, resiNumber);
    } else {
      console.log("❌ No tracking data found");
      console.log(`📦 Tracking Number: ${resiNumber}`);
      console.log(`🚚 Expedition: ${expedition}`);
    }
    
  } catch (error) {
    console.log("❌ ERROR OCCURRED");
    console.log(`📋 Error: ${error.message}`);
    console.log("\n💡 Make sure curl is installed and available in PATH");
  }
}

// Parse and display tracking information
function parseAndDisplayTracking(html, resiNumber) {
  console.log("📦 SHIPPING TRACKING INFORMATION");
  console.log("═".repeat(50));
  
  // Extract basic info
  const expedisiMatch = html.match(/<strong>([^<]*Express[^<]*)<\/strong>/);
  const statusMatch = html.match(/id="status_resi"[^>]*>([^<]*)/);
  const pengirimMatch = html.match(/Dikirim oleh<\/td>[\s\S]*?<td[^>]*>[\s\S]*?<td[^>]*>([^<]*)/);
  const tujuanMatch = html.match(/Dikirim ke<\/td>[\s\S]*?<td[^>]*>[\s\S]*?<td[^>]*>([^<]*)/);
  const tanggalMatch = html.match(/Dikirim tanggal<\/td>[\s\S]*?<td[^>]*>[\s\S]*?<td[^>]*>([^<]*)/);
  
  const expedisi = expedisiMatch ? cleanText(expedisiMatch[1]) : 'N/A';
  const status = statusMatch ? cleanText(statusMatch[1]) : 'N/A';
  const pengirim = pengirimMatch ? cleanText(pengirimMatch[1]) : 'N/A';
  const tujuan = tujuanMatch ? cleanText(tujuanMatch[1]) : 'N/A';
  const tanggalKirim = tanggalMatch ? cleanText(tanggalMatch[1]) : 'N/A';
  
  // Basic Information
  console.log("\n📋 BASIC INFORMATION:");
  console.log("─".repeat(25));
  console.log(`📍 Tracking Number: ${resiNumber}`);
  console.log(`📅 Shipment Date: ${tanggalKirim}`);
  console.log(`🚚 Courier: ${expedisi}`);
  console.log(`📊 Status: ${getStatusIcon(status)} ${status}`);
  
  // Shipper Information
  console.log("\n📤 SHIPPER INFORMATION:");
  console.log("─".repeat(25));
  const shipperInfo = parseShipperInfo(pengirim);
  console.log(`👤 Name: ${shipperInfo.name}`);
  console.log(`📍 Address: ${shipperInfo.address}`);
  
  // Receiver Information
  console.log("\n📥 RECEIVER INFORMATION:");
  console.log("─".repeat(25));
  const receiverInfo = parseReceiverInfo(tujuan);
  console.log(`👤 Name: ${receiverInfo.name}`);
  console.log(`📍 Address: ${receiverInfo.address}`);
  
  // Extract tracking history
  const trackingHistory = extractTrackingHistory(html);
  if (trackingHistory.length > 0) {
    console.log("\n🛣️  TRACKING HISTORY:");
    console.log("─".repeat(25));
    trackingHistory.forEach((item, index) => {
      const icon = getTrackingIcon(item.description, index, trackingHistory.length);
      console.log(`${icon} ${item.date} - ${item.description}`);
    });
  }
  
  console.log("\n" + "═".repeat(50));
}

// Helper functions
function cleanText(text) {
  return text.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, '&').replace(/\s+/g, ' ').trim();
}

function getStatusIcon(status) {
  const statusLower = status.toLowerCase();
  if (statusLower.includes('delivered')) return "✅";
  if (statusLower.includes('transit')) return "🚛";
  if (statusLower.includes('process')) return "⚙️";
  return "📋";
}

function parseShipperInfo(pengirim) {
  const clean = cleanText(pengirim);
  if (clean.includes('WH ')) {
    return {
      name: 'WAREHOUSE',
      address: clean.replace('WH ', '')
    };
  }
  return { name: clean || 'N/A', address: 'N/A' };
}

function parseReceiverInfo(tujuan) {
  const clean = cleanText(tujuan);
  const words = clean.split(' ');
  return {
    name: words.slice(0, 2).join(' ') || 'N/A',
    address: words.slice(2).join(' ') || 'N/A'
  };
}

function extractTrackingHistory(html) {
  const history = [];
  const tableRegex = /<tr[^>]*>[\s\S]*?<\/tr>/g;
  const matches = html.match(tableRegex) || [];
  
  matches.forEach(row => {
    const cellRegex = /<td[^>]*>([\s\S]*?)<\/td>/g;
    const cells = [];
    let match;
    while ((match = cellRegex.exec(row)) !== null) {
      cells.push(cleanText(match[1]));
    }
    
    if (cells.length >= 2) {
      const date = cells[0];
      const desc = cells[1];
      if (date && desc && !date.includes('Tanggal') && date.length > 5) {
        history.push({ date, description: desc });
      }
    }
  });
  
  return history;
}

function getTrackingIcon(description, index, total) {
  const desc = description.toLowerCase();
  if (desc.includes('picked up')) return '📦';
  if (desc.includes('sorting')) return '🏭';
  if (desc.includes('received')) return '📍';
  if (desc.includes('process')) return '⚙️';
  if (desc.includes('courier')) return '🚛';
  if (desc.includes('delivered')) return '✅';
  if (index === 0) return '🏁';
  if (index === total - 1) return '🎯';
  return '📍';
}

// Main execution
const resiNumber = process.argv[2];
const expedition = process.argv[3] || "JNE";

if (!resiNumber) {
  console.log("📦 RESI TRACKING TOOL");
  console.log("═".repeat(30));
  console.log("❌ Please provide a tracking number");
  console.log("\n📋 Usage:");
  console.log("   node track-simple.js <tracking_number> [expedition]");
  console.log("\n📝 Examples:");
  console.log("   node track-simple.js CSS0925755453307 JNE");
  console.log("   node track-simple.js JET123456789 JET");
  process.exit(1);
}

trackResi(resiNumber, expedition);
