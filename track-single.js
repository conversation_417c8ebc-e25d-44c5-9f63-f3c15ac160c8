#!/usr/bin/env node

// Single file resi tracking app - no dependencies needed except Node.js built-ins
import https from 'https';
import http from 'http';
import { URL } from 'url';

// Simple HTTP request function
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ...options.headers
      },
      rejectUnauthorized: false // Allow self-signed certificates
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => resolve({ data, status: res.statusCode }));
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Extract values from HTML using simple regex
function extractValue(html, name) {
  const regex = new RegExp(`name="${name}"\\s+value="([^"]*)"`, 'i');
  const match = html.match(regex);
  return match ? match[1] : null;
}

// Get CSRF tokens
async function getCSRFData(resiNumber, expedition = "JNE") {
  const url = `https://cekresi.com/?noresi=${resiNumber}&e=${expedition}`;
  const response = await makeRequest(url);
  
  return {
    viewstate: extractValue(response.data, 'viewstate'),
    secret_key: extractValue(response.data, 'secret_key')
  };
}

// Simple tracking function
async function trackResi(resiNumber, expedition = "JNE") {
  try {
    console.log("Getting tracking data...");
    
    // Get CSRF tokens
    const csrf = await getCSRFData(resiNumber, expedition);
    
    if (!csrf.viewstate || !csrf.secret_key) {
      throw new Error('Failed to get CSRF tokens');
    }
    
    // Use fixed timers value that works
    const timers = "kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D";
    
    // Get tracking data
    const postData = `viewstate=${csrf.viewstate}&secret_key=${csrf.secret_key}&e=${expedition}&noresi=${resiNumber}&timers=${timers}`;
    
    const response = await makeRequest(
      'https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Origin': 'https://cekresi.com',
          'Referer': 'https://cekresi.com/'
        },
        body: postData
      }
    );
    
    if (response.data && response.data.includes('alert-success')) {
      console.log("✅ Tracking data retrieved successfully!");
      console.log("");
      parseAndDisplayTracking(response.data, resiNumber);
    } else {
      console.log("❌ No tracking data found");
      console.log(`📦 Tracking Number: ${resiNumber}`);
      console.log(`🚚 Expedition: ${expedition}`);
    }
    
  } catch (error) {
    console.log("❌ ERROR OCCURRED");
    console.log(`📋 Error: ${error.message}`);
  }
}

// Parse and display tracking information
function parseAndDisplayTracking(html, resiNumber) {
  console.log("📦 SHIPPING TRACKING INFORMATION");
  console.log("═".repeat(50));
  
  // Extract basic info using regex
  const expedisi = extractTextBetween(html, '<strong>', '</strong>', 1) || 'N/A';
  const status = extractTextBetween(html, 'id="status_resi">', '<', 0) || 'N/A';
  const pengirim = extractTextBetween(html, 'Dikirim oleh</td>', '</td>', 2) || 'N/A';
  const tujuan = extractTextBetween(html, 'Dikirim ke</td>', '</td>', 2) || 'N/A';
  const tanggalKirim = extractTextBetween(html, 'Dikirim tanggal</td>', '</td>', 2) || 'N/A';
  
  // Basic Information
  console.log("\n📋 BASIC INFORMATION:");
  console.log("─".repeat(25));
  console.log(`📍 Tracking Number: ${resiNumber}`);
  console.log(`📅 Shipment Date: ${cleanText(tanggalKirim)}`);
  console.log(`🚚 Courier: ${cleanText(expedisi)}`);
  console.log(`📊 Status: ${getStatusIcon(status)} ${cleanText(status)}`);
  
  // Shipper Information
  console.log("\n📤 SHIPPER INFORMATION:");
  console.log("─".repeat(25));
  const shipperInfo = parseShipperInfo(pengirim);
  console.log(`👤 Name: ${shipperInfo.name}`);
  console.log(`📍 Address: ${shipperInfo.address}`);
  
  // Receiver Information
  console.log("\n📥 RECEIVER INFORMATION:");
  console.log("─".repeat(25));
  const receiverInfo = parseReceiverInfo(tujuan);
  console.log(`👤 Name: ${receiverInfo.name}`);
  console.log(`📍 Address: ${receiverInfo.address}`);
  
  // Extract tracking history
  const trackingHistory = extractTrackingHistory(html);
  if (trackingHistory.length > 0) {
    console.log("\n🛣️  TRACKING HISTORY:");
    console.log("─".repeat(25));
    trackingHistory.forEach((item, index) => {
      const icon = getTrackingIcon(item.description, index, trackingHistory.length);
      console.log(`${icon} ${item.date} - ${item.description}`);
    });
  }
  
  console.log("\n" + "═".repeat(50));
}

// Helper functions
function extractTextBetween(html, start, end, occurrence = 0) {
  const startIndex = html.indexOf(start);
  if (startIndex === -1) return null;
  
  const contentStart = startIndex + start.length;
  const endIndex = html.indexOf(end, contentStart);
  if (endIndex === -1) return null;
  
  return html.substring(contentStart, endIndex).trim();
}

function cleanText(text) {
  return text.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
}

function getStatusIcon(status) {
  const statusLower = status.toLowerCase();
  if (statusLower.includes('delivered')) return "✅";
  if (statusLower.includes('transit')) return "🚛";
  if (statusLower.includes('process')) return "⚙️";
  return "📋";
}

function parseShipperInfo(pengirim) {
  const clean = cleanText(pengirim);
  if (clean.includes('WH ')) {
    return {
      name: 'WAREHOUSE',
      address: clean.replace('WH ', '')
    };
  }
  return { name: clean || 'N/A', address: 'N/A' };
}

function parseReceiverInfo(tujuan) {
  const clean = cleanText(tujuan);
  const words = clean.split(' ');
  return {
    name: words.slice(0, 2).join(' ') || 'N/A',
    address: words.slice(2).join(' ') || 'N/A'
  };
}

function extractTrackingHistory(html) {
  const history = [];
  const tableRegex = /<tr[^>]*>[\s\S]*?<\/tr>/g;
  const matches = html.match(tableRegex) || [];
  
  matches.forEach(row => {
    const cells = row.match(/<td[^>]*>([\s\S]*?)<\/td>/g) || [];
    if (cells.length >= 2) {
      const date = cleanText(cells[0]);
      const desc = cleanText(cells[1]);
      if (date && desc && !date.includes('Tanggal')) {
        history.push({ date, description: desc });
      }
    }
  });
  
  return history;
}

function getTrackingIcon(description, index, total) {
  const desc = description.toLowerCase();
  if (desc.includes('picked up')) return '📦';
  if (desc.includes('sorting')) return '🏭';
  if (desc.includes('received')) return '📍';
  if (desc.includes('process')) return '⚙️';
  if (desc.includes('courier')) return '🚛';
  if (desc.includes('delivered')) return '✅';
  if (index === 0) return '🏁';
  if (index === total - 1) return '🎯';
  return '📍';
}

// Main execution
const resiNumber = process.argv[2];
const expedition = process.argv[3] || "JNE";

if (!resiNumber) {
  console.log("📦 RESI TRACKING TOOL");
  console.log("═".repeat(30));
  console.log("❌ Please provide a tracking number");
  console.log("\n📋 Usage:");
  console.log("   node track-single.js <tracking_number> [expedition]");
  console.log("\n📝 Examples:");
  console.log("   node track-single.js CSS0925755453307 JNE");
  console.log("   node track-single.js JET123456789 JET");
  process.exit(1);
}

trackResi(resiNumber, expedition);
