@echo off
setlocal enabledelayedexpansion

REM Simple curl batch script to track resi without Node.js
REM Usage: curl-tracking.bat <resi_number> [expedition]
REM Example: curl-tracking.bat CSS0925755453307 JNE

set RESI_NUMBER=%1
set EXPEDITION=%2
if "%EXPEDITION%"=="" set EXPEDITION=JNE

if "%RESI_NUMBER%"=="" (
    echo ❌ Please provide a resi number
    echo Usage: curl-tracking.bat ^<resi_number^> [expedition]
    echo Example: curl-tracking.bat CSS0925755453307 JNE
    exit /b 1
)

echo 🔍 Tracking resi: %RESI_NUMBER% with expedition: %EXPEDITION%
echo ==================================================

REM Step 1: Get CSRF tokens from cekresi.com
echo 📋 Step 1: Getting CSRF tokens...

curl -s "https://cekresi.com/?noresi=%RESI_NUMBER%&e=%EXPEDITION%" ^
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" ^
  -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8" ^
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" ^
  -H "Connection: keep-alive" ^
  -o csrf_response.html

REM Extract CSRF tokens (simplified - you might need better parsing)
for /f "tokens=2 delims=^" %%a in ('findstr "name=\""viewstate\"" value=\""" csrf_response.html') do set VIEWSTATE=%%a
for /f "tokens=2 delims=^" %%a in ('findstr "name=\""secret_key\"" value=\""" csrf_response.html') do set SECRET_KEY=%%a

REM Clean up the extracted values (remove quotes)
set VIEWSTATE=%VIEWSTATE:"=%
set SECRET_KEY=%SECRET_KEY:"=%

if "%VIEWSTATE%"=="" (
    echo ❌ Failed to get CSRF tokens
    del csrf_response.html
    exit /b 1
)

echo ✅ CSRF tokens obtained
echo    - viewstate: %VIEWSTATE:~0,20%...
echo    - secret_key: %SECRET_KEY:~0,20%...

REM Step 2: Use encrypted timers (simplified version)
set TIMERS=kRNxDiSZ%%2BCLoe%%2BuFrnFrwLtDxJwvxH4%%2BvJdPUhIEBys%%3D

echo.
echo 🔐 Step 2: Using encrypted timers...
echo ✅ Timers: %TIMERS:~0,30%...

REM Step 3: Get tracking data
echo.
echo 📦 Step 3: Getting tracking data...

curl -s "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" ^
  -H "Accept: */*" ^
  -H "Accept-Language: en-US,en;q=0.9,id;q=0.8" ^
  -H "Connection: keep-alive" ^
  -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" ^
  -H "Origin: https://cekresi.com" ^
  -H "Referer: https://cekresi.com/" ^
  -H "Sec-Fetch-Dest: empty" ^
  -H "Sec-Fetch-Mode: cors" ^
  -H "Sec-Fetch-Site: same-site" ^
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" ^
  -H "sec-ch-ua: \"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"" ^
  -H "sec-ch-ua-mobile: ?0" ^
  -H "sec-ch-ua-platform: \"Windows\"" ^
  --data-raw "viewstate=%VIEWSTATE%&secret_key=%SECRET_KEY%&e=%EXPEDITION%&noresi=%RESI_NUMBER%&timers=%TIMERS%" ^
  -o tracking_response.html

echo ✅ Tracking response received!
echo.
echo 📋 RAW RESPONSE:
echo ================
type tracking_response.html
echo.

echo 📦 Response saved to tracking_response.html
echo 💡 For complete parsing, use the Node.js version or implement HTML parsing

REM Clean up temporary files
del csrf_response.html
del tracking_response.html

endlocal
