#!/usr/bin/env node

/**
 * Resi Tracker Bot Wrapper
 * This wrapper ensures the tracking script runs with correct paths for bot integration
 * Usage: node track-resi-wrapper.js <resi_number> [expedition]
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { existsSync } from 'fs';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const SCRIPT_DIR = __dirname; // Current directory
const SCRIPT_FILE = join(SCRIPT_DIR, 'test-tracking.js');

function runTrackingScript(resiNumber, expedition = 'JNE') {
    return new Promise((resolve, reject) => {
        // Check if script file exists
        if (!existsSync(SCRIPT_FILE)) {
            reject(new Error(`Script file not found: ${SCRIPT_FILE}`));
            return;
        }

        // Check if node_modules exists
        const nodeModulesPath = join(SCRIPT_DIR, 'node_modules');
        if (!existsSync(nodeModulesPath)) {
            reject(new Error(`node_modules not found in ${SCRIPT_DIR}. Please run 'npm install' first.`));
            return;
        }

        // Spawn the tracking script with absolute paths
        const child = spawn('node', [SCRIPT_FILE, resiNumber, expedition], {
            cwd: SCRIPT_DIR,
            stdio: 'inherit',
            env: {
                ...process.env,
                NODE_PATH: join(SCRIPT_DIR, 'node_modules')
            }
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve(code);
            } else {
                reject(new Error(`Script exited with code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🤖 RESI TRACKER BOT WRAPPER');
        console.log('═'.repeat(40));
        console.log('❌ Please provide a tracking number');
        console.log('\n📋 Usage:');
        console.log('   node track-resi-wrapper.js <resi_number> [expedition]');
        console.log('\n📝 Examples:');
        console.log('   node track-resi-wrapper.js CSS0925755453307 JNE');
        console.log('   node track-resi-wrapper.js JET123456789 JET');
        console.log('\n🚚 Available expeditions:');
        console.log('   JNE, JET, TIKI, POS, SICEPAT, ANTERAJA, etc.');
        console.log('\n📁 Script Directory:', SCRIPT_DIR);
        console.log('📄 Script File:', SCRIPT_FILE);
        process.exit(1);
    }

    const resiNumber = args[0];
    const expedition = args[1] || 'JNE';

    try {
        await runTrackingScript(resiNumber, expedition);
    } catch (error) {
        console.error('❌ Error running tracking script:');
        console.error(error.message);
        process.exit(1);
    }
}

main();
