#!/bin/bash

# Resi Tracker Bot Wrapper Script
# This script uses absolute paths for bot integration
# Usage: ./track-resi-bot.sh <resi_number> [expedition]

# Configuration - Update these paths according to your setup
SCRIPT_DIR="/home/<USER>/bot/cek-resi"  # Update this to your actual path
NODE_PATH="/usr/bin/node"        # Update this to your node path
SCRIPT_FILE="$SCRIPT_DIR/test-tracking.js"

# Function to get absolute path of script directory
get_script_dir() {
    local script_path="$(readlink -f "${BASH_SOURCE[0]}")"
    dirname "$script_path"
}

# Auto-detect script directory if not manually set
if [ ! -d "$SCRIPT_DIR" ]; then
    SCRIPT_DIR="$(get_script_dir)"
fi

# Check if parameters are provided
if [ $# -eq 0 ]; then
    echo "❌ Please provide a tracking number"
    echo "Usage: $0 <resi_number> [expedition]"
    echo "Example: $0 CSS0925755453307 JNE"
    exit 1
fi

RESI_NUMBER="$1"
EXPEDITION="${2:-JNE}"

# Check if Node.js exists
if [ ! -f "$NODE_PATH" ]; then
    # Try to find node in common locations
    for path in /usr/bin/node /usr/local/bin/node /opt/node/bin/node ~/.nvm/versions/node/*/bin/node; do
        if [ -f "$path" ]; then
            NODE_PATH="$path"
            break
        fi
    done
    
    if [ ! -f "$NODE_PATH" ]; then
        echo "❌ Node.js not found. Please install Node.js or update NODE_PATH in this script."
        exit 1
    fi
fi

# Check if script file exists
if [ ! -f "$SCRIPT_FILE" ]; then
    echo "❌ Script file not found: $SCRIPT_FILE"
    echo "Please update SCRIPT_DIR in this script to point to the correct directory."
    exit 1
fi

# Check if we're in the right directory for dependencies
cd "$SCRIPT_DIR" || {
    echo "❌ Cannot change to script directory: $SCRIPT_DIR"
    exit 1
}

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "❌ node_modules not found in $SCRIPT_DIR"
    echo "Please run 'npm install' in the script directory first."
    exit 1
fi

# Run the tracking script with full paths
"$NODE_PATH" "$SCRIPT_FILE" "$RESI_NUMBER" "$EXPEDITION"
