# 📦 Resi Tracker Deployment Guide

## 🎯 **Files to <PERSON><PERSON> to Another PC**

### **Essential Files (Required):**
```
📄 test-tracking.js     - Main tracking script
📄 function.js          - Core tracking functions  
📄 manual.js            - Encryption library
📄 package.json         - Dependencies configuration
📄 setup.bat            - Windows setup script
📄 setup.sh             - Linux/Mac setup script
```

### **Optional Files:**
```
📄 index.js             - HTTP API server (if needed)
📄 TEST-README.md       - Documentation
📄 DEPLOYMENT-GUIDE.md  - This guide
```

## 🚀 **Quick Deployment Steps**

### **Method 1: Manual Setup**

1. **Copy the 4 essential files** to the new PC
2. **Install Node.js** if not already installed: https://nodejs.org/
3. **Open terminal/command prompt** in the folder with the files
4. **Install dependencies:**
   ```bash
   npm install
   ```
5. **Test it:**
   ```bash
   node test-tracking.js CSS0925755453307 JNE
   ```

### **Method 2: Automated Setup (Recommended)**

1. **Copy all files** including setup scripts
2. **Run the setup script:**
   
   **Windows:**
   ```cmd
   setup.bat
   ```
   
   **Linux/Mac:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Test it:**
   ```bash
   node test-tracking.js CSS0925755453307 JNE
   ```

## 📁 **Creating a Portable Package**

### **Option A: ZIP Package**
1. Create a folder named `resi-tracker`
2. Copy all essential files into it
3. Zip the folder
4. Share the ZIP file

### **Option B: GitHub Repository**
1. Create a GitHub repository
2. Upload all files
3. Share the repository link
4. Users can clone with: `git clone <repo-url>`

## 🔧 **System Requirements**

### **Minimum Requirements:**
- ✅ Node.js 14+ (LTS recommended)
- ✅ npm (comes with Node.js)
- ✅ Internet connection
- ✅ 10MB free disk space

### **Supported Operating Systems:**
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Linux (Ubuntu, CentOS, etc.)

## 📋 **Troubleshooting**

### **Common Issues:**

#### **1. "node is not recognized"**
**Solution:** Install Node.js from https://nodejs.org/

#### **2. "npm install fails"**
**Solutions:**
- Check internet connection
- Try: `npm install --force`
- Clear npm cache: `npm cache clean --force`

#### **3. "Permission denied" (Linux/Mac)**
**Solution:** 
```bash
chmod +x setup.sh
sudo npm install -g npm@latest
```

#### **4. "Cannot find module"**
**Solution:** Make sure all 4 essential files are in the same folder

## 🎯 **Usage Examples**

```bash
# JNE tracking
node test-tracking.js CSS0925755453307 JNE

# J&T Express
node test-tracking.js JET123456789 JET

# TIKI
node test-tracking.js TIKI123456789 TIKI

# Default to JNE
node test-tracking.js CSS0925755453307
```

## 📦 **Package Contents Verification**

After copying, verify you have these files:

```
your-folder/
├── 📄 test-tracking.js    ✅ Main script
├── 📄 function.js         ✅ Core functions
├── 📄 manual.js           ✅ Encryption
├── 📄 package.json        ✅ Dependencies
├── 📄 setup.bat           📋 Windows setup
├── 📄 setup.sh            📋 Linux/Mac setup
└── 📁 node_modules/       📋 Created after npm install
```

## 🌐 **Network Requirements**

The app needs internet access to:
- ✅ Connect to `cekresi.com` (get CSRF tokens)
- ✅ Connect to `apa1.cekresi.com` (get tracking data)

**Firewall/Proxy:** Make sure these domains are accessible.

## 🔒 **Security Notes**

- ✅ No sensitive data is stored locally
- ✅ Only makes HTTPS requests to official courier APIs
- ✅ No personal information is collected
- ✅ Safe to use on any computer

## 💡 **Pro Tips**

### **For Easy Distribution:**
1. Create a batch file for quick access:
   ```batch
   @echo off
   node test-tracking.js %1 %2
   pause
   ```
   Save as `track.bat`, then use: `track CSS0925755453307 JNE`

2. Create an alias (Linux/Mac):
   ```bash
   echo 'alias track="node test-tracking.js"' >> ~/.bashrc
   source ~/.bashrc
   ```
   Then use: `track CSS0925755453307 JNE`

### **For Multiple PCs:**
- Use a shared network drive
- Set up a simple HTTP server
- Create a Docker container
- Use cloud storage (Dropbox, Google Drive, etc.)

## 📞 **Support**

If you encounter issues:
1. Check this troubleshooting guide
2. Verify all files are present
3. Ensure Node.js is properly installed
4. Test with the known working resi: `CSS0925755453307`

---

**That's it! Your resi tracker is ready to use on any computer! 🎉**
