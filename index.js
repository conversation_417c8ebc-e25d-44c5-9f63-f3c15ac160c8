import { Hono } from "hono";
import { serve } from "@hono/node-server";
import { getCSRFData, encryptTimers, getResiTracking } from "./function.js";

const app = new Hono();

app.get("/cek-resi/:number", async (c) => {
  const number = c.req.param("number");
  const expedition = c.req.query("e") || "JNE"; // Default to JNE, allow override via query param

  if (!number || number.trim() === "") {
    return c.json({ error: "Nomor resi tidak boleh kosong" }, 400);
  }

  try {
    console.log(`Tracking resi: ${number} with expedition: ${expedition}`);
    const csrf = await getCSRFData(number, expedition);
    console.log("CSRF data obtained:", {
      viewstate: csrf.viewstate ? `${csrf.viewstate.substring(0, 20)}...` : null,
      secret_key: csrf.secret_key ? `${csrf.secret_key.substring(0, 20)}...` : null
    });

    const timers = await encryptTimers(number);
    console.log("Timers encrypted:", timers.substring(0, 30) + "...");

    const trackingData = await getResiTracking(number, csrf, timers, expedition);
    console.log("Tracking data result:", trackingData.valid ? "Success" : "Failed");

    return c.json({ status: 200, data: trackingData });
  } catch (error) {
    console.error("Error:", error.message);
    console.error("Stack:", error.stack);
    return c.json({
      status: 500,
      message: "Terjadi kesalahan saat memproses resi",
      error: error.message
    });
  }
});

app.get("/", (c) => {
  return c.json({ status: 200, author: 'Romi Muharom', message: "Selamat datang di API Cek Resi Indonesia, endpoint ada di /cek-resi/:noresi" });
});

app.notFound((c) => c.json({ error: "Halaman yang kamu akses tidak ada" }, 404));

app.onError((c) => c.json({ error: "Terjadi kesalahan pada server" }, 500));

const port = process.env.PORT || 3001;
console.log(`Server running on http://localhost:${port}`);
serve({ fetch: app.fetch, port });
