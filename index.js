import { Hono } from "hono";
import { serve } from "@hono/node-server";
import { getCSRFData, encryptTimers, getResiTracking } from "./function.js";

// Helper function to format tracking data for better readability
function formatTrackingData(data) {
  if (!data.valid) return data;

  const trackingInfo = data.data;

  // Parse shipper information
  const shipperInfo = parseShipperInfo(trackingInfo.pengirim);

  // Parse receiver information
  const receiverInfo = parseReceiverInfo(trackingInfo.tujuan, trackingInfo.penerima);

  // Format tracking history with icons
  const trackingHistory = trackingInfo.perjalanan?.map((item, index) => ({
    date: item.tanggal,
    description: item.keterangan,
    icon: getTrackingIcon(item.keterangan, index, trackingInfo.perjalanan.length)
  })) || [];

  return {
    valid: true,
    basicInfo: {
      trackingNumber: trackingInfo.noResi,
      shipmentDate: trackingInfo.tanggalKirim,
      courier: trackingInfo.expedisi,
      status: trackingInfo.status,
      statusIcon: getStatusIcon(trackingInfo.status)
    },
    shipper: shipperInfo,
    receiver: receiverInfo,
    trackingHistory: trackingHistory
  };
}

// Helper functions (same as in test-tracking.js)
function parseShipperInfo(pengirim) {
  if (!pengirim) return { name: 'N/A', address: 'N/A' };

  const parts = pengirim.split(/(?=[A-Z]{2,})/);

  if (parts.length >= 2) {
    return {
      name: parts[0].trim(),
      address: parts.slice(1).join(' ').trim()
    };
  }

  return {
    name: pengirim.includes('WH ') ? pengirim : 'N/A',
    address: pengirim.includes('WH ') ? 'Warehouse' : pengirim
  };
}

function parseReceiverInfo(tujuan, penerima) {
  let name = 'N/A';
  let address = 'N/A';
  let deliveryStatus = 'N/A';

  if (tujuan) {
    const parts = tujuan.split(/(?=[A-Z]{2,})/);
    if (parts.length >= 2) {
      name = parts[0].trim();
      address = parts.slice(1).join(' ').trim();
    } else {
      address = tujuan;
    }
  }

  if (penerima) {
    if (penerima.includes('DELIVERED TO')) {
      const match = penerima.match(/DELIVERED TO \[([^\]]+)\]/);
      if (match) {
        const deliveryInfo = match[1].split(' | ');
        if (deliveryInfo.length >= 3) {
          name = deliveryInfo[0].trim();
          deliveryStatus = `Delivered on ${deliveryInfo[1]} at ${deliveryInfo[2]}`;
        }
      }
    } else {
      deliveryStatus = penerima;
    }
  }

  return { name, address, deliveryStatus };
}

function getStatusIcon(status) {
  if (!status) return "❓";

  const statusLower = status.toLowerCase();
  if (statusLower.includes('delivered')) return "✅";
  if (statusLower.includes('transit') || statusLower.includes('process')) return "🚛";
  if (statusLower.includes('picked') || statusLower.includes('received')) return "📦";
  if (statusLower.includes('pending') || statusLower.includes('waiting')) return "⏳";
  if (statusLower.includes('failed') || statusLower.includes('error')) return "❌";

  return "📋";
}

function getTrackingIcon(keterangan, index, total) {
  const step = keterangan.toLowerCase();

  if (step.includes('picked up') || step.includes('shipment picked')) return '📦';
  if (step.includes('received at') && step.includes('sorting')) return '🏭';
  if (step.includes('received at') && !step.includes('sorting')) return '📍';
  if (step.includes('processed at') || step.includes('process and forward')) return '⚙️';
  if (step.includes('with delivery courier') || step.includes('out for delivery')) return '🚛';
  if (step.includes('delivered to')) return '✅';
  if (step.includes('transit') || step.includes('forward')) return '🚚';

  if (index === 0) return '🏁';
  if (index === total - 1) return '🎯';

  return '📍';
}

const app = new Hono();

app.get("/cek-resi/:number", async (c) => {
  const number = c.req.param("number");
  const expedition = c.req.query("e") || "JNE"; // Default to JNE, allow override via query param

  if (!number || number.trim() === "") {
    return c.json({ error: "Nomor resi tidak boleh kosong" }, 400);
  }

  try {
    console.log(`Tracking resi: ${number} with expedition: ${expedition}`);
    const csrf = await getCSRFData(number, expedition);
    console.log("CSRF data obtained:", {
      viewstate: csrf.viewstate ? `${csrf.viewstate.substring(0, 20)}...` : null,
      secret_key: csrf.secret_key ? `${csrf.secret_key.substring(0, 20)}...` : null
    });

    const timers = await encryptTimers(number);
    console.log("Timers encrypted:", timers.substring(0, 30) + "...");

    const trackingData = await getResiTracking(number, csrf, timers, expedition);
    console.log("Tracking data result:", trackingData.valid ? "Success" : "Failed");

    // Format the data for better readability
    const formattedData = formatTrackingData(trackingData);

    return c.json({ status: 200, data: formattedData });
  } catch (error) {
    console.error("Error:", error.message);
    console.error("Stack:", error.stack);
    return c.json({
      status: 500,
      message: "Terjadi kesalahan saat memproses resi",
      error: error.message
    });
  }
});

app.get("/", (c) => {
  return c.json({ status: 200, author: 'Romi Muharom', message: "Selamat datang di API Cek Resi Indonesia, endpoint ada di /cek-resi/:noresi" });
});

app.notFound((c) => c.json({ error: "Halaman yang kamu akses tidak ada" }, 404));

app.onError((c) => c.json({ error: "Terjadi kesalahan pada server" }, 500));

const port = process.env.PORT || 3001;
console.log(`Server running on http://localhost:${port}`);
serve({ fetch: app.fetch, port });
