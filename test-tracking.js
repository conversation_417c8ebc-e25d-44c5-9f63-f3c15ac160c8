import { getCSRFData, encryptTimers, getResiTracking } from "./function.js";

// Test function to manually run tracking
async function testTracking(resiNumber) {
  console.log(`🔍 Testing tracking for resi number: ${resiNumber}`);
  console.log("=" .repeat(50));
  
  try {
    // Step 1: Get CSRF data
    console.log("📋 Step 1: Getting CSRF data...");
    const csrf = await getCSRFData(resiNumber);
    console.log("✅ CSRF data obtained:");
    console.log(`   - viewstate: ${csrf.viewstate ? csrf.viewstate.substring(0, 50) + '...' : 'null'}`);
    console.log(`   - secret_key: ${csrf.secret_key ? csrf.secret_key.substring(0, 20) + '...' : 'null'}`);
    
    // Step 2: Encrypt timers
    console.log("\n🔐 Step 2: Encrypting timers...");
    const timers = await encryptTimers(resiNumber);
    console.log("✅ Timers encrypted:");
    console.log(`   - encrypted value: ${timers.substring(0, 50)}...`);
    
    // Step 3: Get tracking data
    console.log("\n📦 Step 3: Getting tracking data...");
    const trackingData = await getResiTracking(resiNumber, csrf, timers);
    
    if (trackingData.valid) {
      console.log("✅ Tracking data retrieved successfully!");
      console.log("\n📋 TRACKING INFORMATION:");
      console.log("=" .repeat(30));
      console.log(`Expedisi: ${trackingData.data.expedisi}`);
      console.log(`No Resi: ${trackingData.data.noResi}`);
      console.log(`Pengirim: ${trackingData.data.pengirim}`);
      console.log(`Tujuan: ${trackingData.data.tujuan}`);
      console.log(`Status: ${trackingData.data.status}`);
      console.log(`Tanggal Kirim: ${trackingData.data.tanggalKirim}`);
      console.log(`Penerima: ${trackingData.data.penerima}`);
      
      if (trackingData.data.perjalanan && trackingData.data.perjalanan.length > 0) {
        console.log("\n🛣️  PERJALANAN PAKET:");
        console.log("=" .repeat(30));
        trackingData.data.perjalanan.forEach((item, index) => {
          console.log(`${index + 1}. ${item.tanggal} - ${item.keterangan}`);
        });
      }
    } else {
      console.log("❌ Failed to get tracking data:");
      console.log(`   Message: ${trackingData.message || 'Unknown error'}`);
    }
    
  } catch (error) {
    console.error("❌ Error during tracking test:");
    console.error(`   Error: ${error.message}`);
    console.error(`   Stack: ${error.stack}`);
  }
}

// Get resi number from command line arguments or use default
const resiNumber = process.argv[2];

if (!resiNumber) {
  console.log("❌ Please provide a resi number as argument");
  console.log("Usage: node test-tracking.js <resi_number>");
  console.log("Example: node test-tracking.js JET123456789");
  process.exit(1);
}

// Run the test
testTracking(resiNumber);
