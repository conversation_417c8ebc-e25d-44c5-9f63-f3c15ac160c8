import { getCSRFData, encryptTimers, getResiTracking } from "./function.js";

// Enhanced display function for shipping information
function displayShippingInfo(data) {
  console.log("📦 SHIPPING TRACKING INFORMATION");
  console.log("═".repeat(50));

  // Basic Information
  console.log("\n📋 BASIC INFORMATION:");
  console.log("─".repeat(25));
  console.log(`📍 Tracking Number: ${data.noResi || 'N/A'}`);
  console.log(`📅 Shipment Date: ${data.tanggalKirim || 'N/A'}`);
  console.log(`🚚 Courier: ${data.expedisi || 'N/A'}`);
  console.log(`📊 Status: ${getStatusWithIcon(data.status)}`);

  // Shipper Information
  console.log("\n📤 SHIPPER INFORMATION:");
  console.log("─".repeat(25));
  const shipperInfo = parseShipperInfo(data.pengirim);
  console.log(`👤 Name: ${shipperInfo.name}`);
  console.log(`📍 Address: ${shipperInfo.address}`);

  // Receiver Information
  console.log("\n📥 RECEIVER INFORMATION:");
  console.log("─".repeat(25));
  const receiverInfo = parseReceiverInfo(data.tujuan, data.penerima);
  console.log(`👤 Name: ${receiverInfo.name}`);
  console.log(`📍 Address: ${receiverInfo.address}`);
  console.log(`📋 Delivery Status: ${receiverInfo.deliveryStatus}`);

  // Tracking History
  if (data.perjalanan && data.perjalanan.length > 0) {
    console.log("\n🛣️  TRACKING HISTORY:");
    console.log("─".repeat(25));
    data.perjalanan.forEach((item, index) => {
      const icon = getTrackingIcon(item.keterangan, index, data.perjalanan.length);
      console.log(`${icon} ${item.tanggal} - ${item.keterangan}`);
    });
  }

  console.log("\n" + "═".repeat(50));
}

// Helper function to get status with appropriate icon
function getStatusWithIcon(status) {
  if (!status) return "❓ Unknown";

  const statusLower = status.toLowerCase();
  if (statusLower.includes('delivered')) return `✅ ${status}`;
  if (statusLower.includes('transit') || statusLower.includes('process')) return `🚛 ${status}`;
  if (statusLower.includes('picked') || statusLower.includes('received')) return `📦 ${status}`;
  if (statusLower.includes('pending') || statusLower.includes('waiting')) return `⏳ ${status}`;
  if (statusLower.includes('failed') || statusLower.includes('error')) return `❌ ${status}`;

  return `📋 ${status}`;
}

// Helper function to parse shipper information
function parseShipperInfo(pengirim) {
  if (!pengirim) return { name: 'N/A', address: 'N/A' };

  // Try to extract name and address from the pengirim field
  // Format usually: "NAME + ADDRESS" or just "ADDRESS"
  const parts = pengirim.split(/(?=[A-Z]{2,})/); // Split on uppercase sequences

  if (parts.length >= 2) {
    return {
      name: parts[0].trim(),
      address: parts.slice(1).join(' ').trim()
    };
  }

  return {
    name: pengirim.includes('WH ') ? pengirim : 'N/A',
    address: pengirim.includes('WH ') ? 'Warehouse' : pengirim
  };
}

// Helper function to parse receiver information
function parseReceiverInfo(tujuan, penerima) {
  let name = 'N/A';
  let address = 'N/A';
  let deliveryStatus = 'N/A';

  // Extract name from tujuan (usually format: "NAME + ADDRESS")
  if (tujuan) {
    const parts = tujuan.split(/(?=[A-Z]{2,})/);
    if (parts.length >= 2) {
      name = parts[0].trim();
      address = parts.slice(1).join(' ').trim();
    } else {
      address = tujuan;
    }
  }

  // Extract delivery status from penerima
  if (penerima) {
    if (penerima.includes('DELIVERED TO')) {
      const match = penerima.match(/DELIVERED TO \[([^\]]+)\]/);
      if (match) {
        const deliveryInfo = match[1].split(' | ');
        if (deliveryInfo.length >= 3) {
          name = deliveryInfo[0].trim();
          deliveryStatus = `✅ Delivered on ${deliveryInfo[1]} at ${deliveryInfo[2]}`;
        }
      }
    } else {
      deliveryStatus = penerima;
    }
  }

  return { name, address, deliveryStatus };
}

// Helper function to get appropriate icon for tracking steps
function getTrackingIcon(keterangan, index, total) {
  const step = keterangan.toLowerCase();

  if (step.includes('picked up') || step.includes('shipment picked')) return '📦';
  if (step.includes('received at') && step.includes('sorting')) return '🏭';
  if (step.includes('received at') && !step.includes('sorting')) return '📍';
  if (step.includes('processed at') || step.includes('process and forward')) return '⚙️';
  if (step.includes('with delivery courier') || step.includes('out for delivery')) return '🚛';
  if (step.includes('delivered to')) return '✅';
  if (step.includes('transit') || step.includes('forward')) return '🚚';

  // Default icons based on position
  if (index === 0) return '🏁'; // Start
  if (index === total - 1) return '🎯'; // End

  return '📍'; // Default
}

// Test function to manually run tracking
async function testTracking(resiNumber, expedition = "JNE") {
  try {
    // Step 3: Get tracking data (hide steps 1 and 2)
    console.log("Getting tracking data...");

    const csrf = await getCSRFData(resiNumber, expedition);
    const timers = await encryptTimers(resiNumber);
    const trackingData = await getResiTracking(resiNumber, csrf, timers, expedition);

    if (trackingData.valid) {
      console.log("✅ Tracking data retrieved successfully!");
      console.log("");

      // Display enhanced shipping information
      displayShippingInfo(trackingData.data);
    } else {
      console.log("❌ Failed to get tracking data:");
      console.log(`   Message: ${trackingData.message || 'Unknown error'}`);
    }

  } catch (error) {
    console.error("❌ Error during tracking test:");
    console.error(`   Error: ${error.message}`);
  }
}

// Get resi number and expedition from command line arguments
const resiNumber = process.argv[2];
const expedition = process.argv[3] || "JNE";

if (!resiNumber) {
  console.log("❌ Please provide a resi number as argument");
  console.log("Usage: node test-tracking.js <resi_number> [expedition]");
  console.log("Example: node test-tracking.js CSS0925755453307 JNE");
  console.log("Example: node test-tracking.js JET123456789 JET");
  console.log("Available expeditions: JNE, JET, TIKI, POS, etc.");
  process.exit(1);
}

// Run the test
testTracking(resiNumber, expedition);
