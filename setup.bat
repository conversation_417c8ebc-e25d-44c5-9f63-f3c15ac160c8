@echo off
echo 📦 RESI TRACKER SETUP
echo ==========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo 💡 Please install Node.js from: https://nodejs.org/
    echo    Download the LTS version and install it.
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
node --version

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not available!
    pause
    exit /b 1
)

echo ✅ npm is available
npm --version
echo.

REM Install dependencies
echo 📥 Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo ✅ Setup completed successfully!
echo.
echo 📋 Usage:
echo    node test-tracking.js ^<resi_number^> [expedition]
echo.
echo 📝 Example:
echo    node test-tracking.js CSS0925755453307 JNE
echo.
echo 🚚 Available expeditions: JNE, JET, TIKI, POS, etc.
echo.
pause
