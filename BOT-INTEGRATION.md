# 🤖 Bot Integration Guide

You're right! When running from a bot or automated script, you need to use full absolute paths. Here are several solutions:

## 🎯 **Quick Solutions**

### **Solution 1: Use Full Absolute Path (Immediate Fix)**
```bash
# Instead of:
node cek-resi/test-tracking.js CSS0925755453307 JNE

# Use:
cd /home/<USER>/cek-resi && node test-tracking.js CSS0925755453307 JNE

# Or with full paths:
/usr/bin/node /home/<USER>/cek-resi/test-tracking.js CSS0925755453307 JNE
```

### **Solution 2: Use the Wrapper Script (Recommended)**
```bash
# Use the wrapper that handles paths automatically:
node /home/<USER>/cek-resi/track-resi-wrapper.js CSS0925755453307 JNE
```

### **Solution 3: Use Bash Wrapper**
```bash
# Use the bash wrapper:
/home/<USER>/cek-resi/track-resi-bot.sh CSS0925755453307 JNE
```

## 🔧 **Setup Instructions**

### **Step 1: Make Scripts Executable**
```bash
cd /home/<USER>/cek-resi
chmod +x track-resi-bot.sh
chmod +x setup-bot-env.sh
chmod +x install-global.sh
```

### **Step 2: Configure Environment**
```bash
# Run the setup script:
./setup-bot-env.sh
```

### **Step 3: Test Integration**
```bash
# Test the integration:
./test-bot-integration.sh
```

## 📋 **Bot Integration Methods**

### **Method 1: Wrapper Script (Easiest)**
```javascript
// In your bot code:
const { exec } = require('child_process');

function trackResi(resiNumber, expedition = 'JNE') {
    return new Promise((resolve, reject) => {
        const command = `node /home/<USER>/cek-resi/track-resi-wrapper.js ${resiNumber} ${expedition}`;
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
                return;
            }
            resolve(stdout);
        });
    });
}

// Usage:
trackResi('CSS0925755453307', 'JNE')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### **Method 2: Direct Path with CD**
```javascript
// In your bot code:
const { exec } = require('child_process');

function trackResi(resiNumber, expedition = 'JNE') {
    return new Promise((resolve, reject) => {
        const command = `cd /home/<USER>/cek-resi && node test-tracking.js ${resiNumber} ${expedition}`;
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
                return;
            }
            resolve(stdout);
        });
    });
}
```

### **Method 3: Spawn with CWD**
```javascript
// In your bot code:
const { spawn } = require('child_process');

function trackResi(resiNumber, expedition = 'JNE') {
    return new Promise((resolve, reject) => {
        const child = spawn('node', ['test-tracking.js', resiNumber, expedition], {
            cwd: '/home/<USER>/cek-resi',
            stdio: 'pipe'
        });

        let output = '';
        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve(output);
            } else {
                reject(new Error(`Process exited with code ${code}`));
            }
        });
    });
}
```

### **Method 4: Global Command**
```bash
# Install as global command:
sudo ./install-global.sh

# Then use from anywhere:
track-resi CSS0925755453307 JNE
```

## 🐍 **Python Bot Integration**
```python
import subprocess
import os

def track_resi(resi_number, expedition='JNE'):
    script_dir = '/home/<USER>/cek-resi'
    script_path = os.path.join(script_dir, 'track-resi-wrapper.js')
    
    try:
        result = subprocess.run(
            ['node', script_path, resi_number, expedition],
            cwd=script_dir,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            return result.stdout
        else:
            raise Exception(f"Error: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        raise Exception("Tracking request timed out")

# Usage:
try:
    result = track_resi('CSS0925755453307', 'JNE')
    print(result)
except Exception as e:
    print(f"Error: {e}")
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. "Cannot find module" Error**
**Solution:** Make sure you're in the correct directory with node_modules
```bash
cd /home/<USER>/cek-resi && node test-tracking.js CSS0925755453307 JNE
```

#### **2. "node: command not found"**
**Solution:** Use full path to node
```bash
/usr/bin/node /home/<USER>/cek-resi/test-tracking.js CSS0925755453307 JNE
```

#### **3. Permission Denied**
**Solution:** Make scripts executable
```bash
chmod +x /home/<USER>/cek-resi/track-resi-bot.sh
```

#### **4. Path Issues**
**Solution:** Update paths in wrapper scripts
```bash
# Edit track-resi-bot.sh and update:
SCRIPT_DIR="/home/<USER>/cek-resi"  # Your actual path
NODE_PATH="/usr/bin/node"        # Your node path
```

## 📁 **File Structure for Bot**
```
/home/<USER>/cek-resi/
├── test-tracking.js           # Main script
├── function.js                # Core functions
├── manual.js                  # Encryption library
├── package.json               # Dependencies
├── node_modules/              # Installed packages
├── track-resi-wrapper.js      # Bot wrapper (Node.js)
├── track-resi-bot.sh          # Bot wrapper (Bash)
├── bot-config.json            # Configuration
└── bot-env.sh                 # Environment setup
```

## 🎯 **Recommended Approach**

### **For Most Bots:**
Use the **wrapper script** method:
```bash
node /home/<USER>/cek-resi/track-resi-wrapper.js CSS0925755453307 JNE
```

### **Why This Works:**
- ✅ Handles all path issues automatically
- ✅ Includes error checking
- ✅ Works from any directory
- ✅ Easy to integrate into any bot
- ✅ Maintains the same output format

### **Quick Test:**
```bash
# Test from any directory:
cd /tmp
node /home/<USER>/cek-resi/track-resi-wrapper.js CSS0925755453307 JNE
```

This should work perfectly for your bot integration! 🤖✅
