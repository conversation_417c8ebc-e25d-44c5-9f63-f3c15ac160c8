#!/bin/bash

# Setup Bot Environment for Resi Tracker
# This script configures the environment for bot integration

echo "🤖 SETTING UP BOT ENVIRONMENT"
echo "═".repeat(40)

# Get current directory
CURRENT_DIR="$(pwd)"
SCRIPT_DIR="$(readlink -f "$(dirname "${BASH_SOURCE[0]}")")"

echo "📂 Current directory: $CURRENT_DIR"
echo "📁 Script directory: $SCRIPT_DIR"

# Check if we're in the right directory
if [ ! -f "test-tracking.js" ]; then
    echo "❌ test-tracking.js not found"
    echo "Please run this script from the cek-resi directory"
    exit 1
fi

# Check Node.js installation
NODE_PATH="$(which node)"
if [ -z "$NODE_PATH" ]; then
    echo "❌ Node.js not found in PATH"
    echo "Please install Node.js or add it to PATH"
    exit 1
fi

echo "✅ Node.js found: $NODE_PATH"
echo "📋 Node.js version: $(node --version)"

# Check npm and dependencies
if [ ! -d "node_modules" ]; then
    echo "📥 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

echo "✅ Dependencies installed"

# Create bot configuration file
cat > "bot-config.json" << EOF
{
  "scriptDir": "$SCRIPT_DIR",
  "nodePath": "$NODE_PATH",
  "scriptFile": "$SCRIPT_DIR/test-tracking.js",
  "wrapperScript": "$SCRIPT_DIR/track-resi-wrapper.js"
}
EOF

echo "✅ Bot configuration created: bot-config.json"

# Create a simple test script
cat > "test-bot-integration.sh" << EOF
#!/bin/bash

# Test Bot Integration
echo "🧪 Testing bot integration..."

# Test with wrapper script
echo "📋 Testing wrapper script:"
node "$SCRIPT_DIR/track-resi-wrapper.js" CSS0925755453307 JNE

echo ""
echo "📋 Testing direct call with full path:"
cd "$SCRIPT_DIR" && node test-tracking.js CSS0925755453307 JNE
EOF

chmod +x "test-bot-integration.sh"

echo "✅ Test script created: test-bot-integration.sh"

# Create environment variables file
cat > "bot-env.sh" << EOF
#!/bin/bash

# Bot Environment Variables
export RESI_TRACKER_DIR="$SCRIPT_DIR"
export RESI_TRACKER_NODE="$NODE_PATH"
export RESI_TRACKER_SCRIPT="$SCRIPT_DIR/test-tracking.js"
export RESI_TRACKER_WRAPPER="$SCRIPT_DIR/track-resi-wrapper.js"

# Function to track resi from anywhere
track_resi() {
    if [ \$# -eq 0 ]; then
        echo "Usage: track_resi <resi_number> [expedition]"
        return 1
    fi
    
    cd "\$RESI_TRACKER_DIR" && "\$RESI_TRACKER_NODE" "\$RESI_TRACKER_SCRIPT" "\$@"
}

echo "🤖 Resi Tracker environment loaded"
echo "📋 Use: track_resi <resi_number> [expedition]"
EOF

echo "✅ Environment file created: bot-env.sh"

echo ""
echo "🎯 SETUP COMPLETE!"
echo "═".repeat(30)
echo ""
echo "📋 For bot integration, use one of these methods:"
echo ""
echo "1️⃣  Use the wrapper script:"
echo "   node $SCRIPT_DIR/track-resi-wrapper.js CSS0925755453307 JNE"
echo ""
echo "2️⃣  Use full path with cd:"
echo "   cd $SCRIPT_DIR && node test-tracking.js CSS0925755453307 JNE"
echo ""
echo "3️⃣  Source the environment and use function:"
echo "   source $SCRIPT_DIR/bot-env.sh"
echo "   track_resi CSS0925755453307 JNE"
echo ""
echo "4️⃣  Use the bash wrapper:"
echo "   $SCRIPT_DIR/track-resi-bot.sh CSS0925755453307 JNE"
echo ""
echo "🧪 Test the integration:"
echo "   ./test-bot-integration.sh"
