# Simplified Resi Tracking Versions

I've created several simplified versions of the resi tracking app for easier use:

## 🎯 **Recommended: Use the Original Node.js Version**

The most reliable version is still the original multi-file Node.js version:

```bash
node test-tracking.js CSS0925755453307 JNE
```

**Why this is recommended:**
- ✅ Proven to work perfectly
- ✅ Clean output with icons
- ✅ Handles all edge cases
- ✅ Uses the working encryption and API calls

## 📁 **Available Simplified Versions**

### 1. **PowerShell Script (Windows)** - `track-resi.ps1`
**Best for Windows users who want a single script**

```powershell
.\track-resi.ps1 CSS0925755453307 JNE
```

**Features:**
- ✅ Single PowerShell file
- ✅ No external dependencies
- ✅ Colored output
- ✅ Works on Windows natively

### 2. **Shell Script (Linux/Mac)** - `track-resi.sh`
**Best for Linux/Mac users**

```bash
chmod +x track-resi.sh
./track-resi.sh CSS0925755453307 JNE
```

**Features:**
- ✅ Single bash script
- ✅ Uses curl (usually pre-installed)
- ✅ Colored output
- ✅ Works on Linux/Mac

### 3. **Single JavaScript File** - `track-single.js`
**Standalone Node.js file with no external dependencies**

```bash
node track-single.js CSS0925755453307 JNE
```

**Features:**
- ✅ Single JavaScript file
- ✅ No npm dependencies
- ✅ Uses only Node.js built-ins
- ⚠️ May have SSL/network issues on some systems

### 4. **Curl-based JavaScript** - `track-simple.js`
**Uses curl commands from Node.js**

```bash
node track-simple.js CSS0925755453307 JNE
```

**Features:**
- ✅ Single JavaScript file
- ✅ Uses proven curl commands
- ❌ Requires curl to be installed

## 🚀 **Creating Standalone Executables**

To create a standalone executable that doesn't require Node.js:

### Install pkg globally:
```bash
npm install -g pkg
```

### Build executables:
```bash
# Windows executable
pkg track-single.js --targets node18-win-x64 --output track-resi.exe

# Linux executable  
pkg track-single.js --targets node18-linux-x64 --output track-resi-linux

# Mac executable
pkg track-single.js --targets node18-macos-x64 --output track-resi-mac

# All platforms
pkg track-single.js --targets node18-win-x64,node18-linux-x64,node18-macos-x64
```

### Use the executable:
```bash
# Windows
track-resi.exe CSS0925755453307 JNE

# Linux/Mac
./track-resi-linux CSS0925755453307 JNE
```

## 📋 **Usage Examples**

All versions use the same command format:

```bash
# JNE tracking
<command> CSS0925755453307 JNE

# J&T Express tracking  
<command> JET123456789 JET

# TIKI tracking
<command> TIKI123456789 TIKI

# Default to JNE if no expedition specified
<command> CSS0925755453307
```

## 🎨 **Expected Output**

All versions produce the same clean output:

```
Getting tracking data...
✅ Tracking data retrieved successfully!

📦 SHIPPING TRACKING INFORMATION
══════════════════════════════════════════════════

📋 BASIC INFORMATION:
─────────────────────────
📍 Tracking Number: CSS0925755453307
📅 Shipment Date: 2025-06-03
🚚 Courier: JNE Express
📊 Status: ✅ Delivered

📤 SHIPPER INFORMATION:
─────────────────────────
👤 Name: WAREHOUSE
📍 Address: DHL PALEMBANG

📥 RECEIVER INFORMATION:
─────────────────────────
👤 Name: SUSAN APRIANSYAH
📍 Address: CURUP TIMUR,REJANG LEBONG

🛣️  TRACKING HISTORY:
─────────────────────────
📦 03-06-2025 13:37 - SHIPMENT PICKED UP BY JNE COURIER
📍 03-06-2025 18:05 - SHIPMENT RECEIVED AT [PALEMBANG]
🏭 03-06-2025 22:48 - RECEIVED AT SORTING CENTER
⚙️ 03-06-2025 22:53 - PROCESSED AT SORTING CENTER
⚙️ 05-06-2025 16:43 - PROCESS AND FORWARD TO DESTINATION
📍 05-06-2025 16:43 - RECEIVED AT WAREHOUSE
📍 06-06-2025 09:34 - RECEIVED AT DESTINATION
🚛 06-06-2025 10:40 - WITH DELIVERY COURIER
✅ 06-06-2025 14:29 - DELIVERED TO RECIPIENT

══════════════════════════════════════════════════
```

## 🔧 **Troubleshooting**

### PowerShell Execution Policy (Windows)
If you get execution policy errors:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Curl Not Found (Windows)
For curl-based versions on Windows:
- Install curl from https://curl.se/windows/
- Or use Windows 10/11 built-in curl
- Or use the PowerShell version instead

### SSL/Network Issues
If you encounter SSL errors:
- Use the original Node.js version (most reliable)
- Or add `-k` flag to curl commands (less secure)

## 💡 **Recommendations**

1. **For daily use**: Stick with the original `node test-tracking.js`
2. **For Windows users**: Use `track-resi.ps1`
3. **For Linux/Mac users**: Use `track-resi.sh`
4. **For distribution**: Create executables with `pkg`
5. **For integration**: Use the API endpoint from `index.js`

The original Node.js version remains the most reliable and feature-complete option!
