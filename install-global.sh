#!/bin/bash

# Install Resi Tracker as Global Command
# This script creates a global command that can be used from anywhere

# Configuration
INSTALL_DIR="/usr/local/bin"
COMMAND_NAME="track-resi"
SCRIPT_DIR="$(pwd)"

# Check if running as root for global installation
if [ "$EUID" -ne 0 ]; then
    echo "🔒 This script needs to be run as root for global installation"
    echo "Please run: sudo $0"
    exit 1
fi

echo "🚀 Installing Resi Tracker as Global Command"
echo "═".repeat(50)

# Check if required files exist
if [ ! -f "test-tracking.js" ]; then
    echo "❌ test-tracking.js not found in current directory"
    echo "Please run this script from the cek-resi directory"
    exit 1
fi

if [ ! -d "node_modules" ]; then
    echo "❌ node_modules not found"
    echo "Please run 'npm install' first"
    exit 1
fi

# Create the global command script
cat > "$INSTALL_DIR/$COMMAND_NAME" << EOF
#!/bin/bash

# Global Resi Tracker Command
# Auto-generated by install-global.sh

SCRIPT_DIR="$SCRIPT_DIR"
NODE_PATH="\$(which node)"

# Check if parameters are provided
if [ \$# -eq 0 ]; then
    echo "📦 RESI TRACKER - GLOBAL COMMAND"
    echo "════════════════════════════════════════"
    echo "❌ Please provide a tracking number"
    echo ""
    echo "📋 Usage:"
    echo "   $COMMAND_NAME <resi_number> [expedition]"
    echo ""
    echo "📝 Examples:"
    echo "   $COMMAND_NAME CSS0925755453307 JNE"
    echo "   $COMMAND_NAME JET123456789 JET"
    echo ""
    echo "🚚 Available expeditions:"
    echo "   JNE, JET, TIKI, POS, SICEPAT, ANTERAJA, etc."
    echo ""
    echo "📁 Script location: \$SCRIPT_DIR"
    exit 1
fi

# Change to script directory and run
cd "\$SCRIPT_DIR" && "\$NODE_PATH" test-tracking.js "\$@"
EOF

# Make the command executable
chmod +x "$INSTALL_DIR/$COMMAND_NAME"

echo "✅ Installation completed!"
echo ""
echo "📋 Usage:"
echo "   $COMMAND_NAME CSS0925755453307 JNE"
echo ""
echo "📁 Installed to: $INSTALL_DIR/$COMMAND_NAME"
echo "📂 Script directory: $SCRIPT_DIR"
echo ""
echo "🎯 You can now use '$COMMAND_NAME' from anywhere!"
