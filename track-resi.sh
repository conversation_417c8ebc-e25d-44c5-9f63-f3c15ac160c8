#!/bin/bash

# Resi Tracking Shell Script
# Usage: ./track-resi.sh <resi_number> [expedition]
# Example: ./track-resi.sh CSS0925755453307 JNE

RESI_NUMBER=$1
EXPEDITION=${2:-JNE}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored text
print_color() {
    echo -e "${1}${2}${NC}"
}

# Function to extract value from HTML
extract_value() {
    local html="$1"
    local name="$2"
    echo "$html" | grep -o "name=\"$name\" value=\"[^\"]*\"" | sed "s/name=\"$name\" value=\"//;s/\"//"
}

# Function to clean HTML text
clean_text() {
    echo "$1" | sed 's/<[^>]*>//g' | sed 's/&nbsp;/ /g' | sed 's/&amp;/\&/g' | tr -s ' ' | xargs
}

# Function to display usage
show_usage() {
    print_color $CYAN "📦 RESI TRACKING TOOL"
    echo "═══════════════════════════════════"
    print_color $RED "❌ Please provide a tracking number"
    echo ""
    print_color $YELLOW "📋 Usage:"
    echo "   ./track-resi.sh <tracking_number> [expedition]"
    echo ""
    print_color $YELLOW "📝 Examples:"
    echo "   ./track-resi.sh CSS0925755453307 JNE"
    echo "   ./track-resi.sh JET123456789 JET"
    echo "   ./track-resi.sh TIKI123456789 TIKI"
    echo ""
    print_color $YELLOW "🚚 Available expeditions:"
    echo "   JNE, JET, TIKI, POS, SICEPAT, ANTERAJA, etc."
}

# Function to get status icon
get_status_icon() {
    local status=$(echo "$1" | tr '[:upper:]' '[:lower:]')
    if [[ $status == *"delivered"* ]]; then
        echo "✅"
    elif [[ $status == *"transit"* ]] || [[ $status == *"process"* ]]; then
        echo "🚛"
    elif [[ $status == *"picked"* ]] || [[ $status == *"received"* ]]; then
        echo "📦"
    else
        echo "📋"
    fi
}

# Function to get tracking icon
get_tracking_icon() {
    local desc=$(echo "$1" | tr '[:upper:]' '[:lower:]')
    local index=$2
    local total=$3
    
    if [[ $desc == *"picked up"* ]]; then
        echo "📦"
    elif [[ $desc == *"sorting"* ]]; then
        echo "🏭"
    elif [[ $desc == *"received"* ]]; then
        echo "📍"
    elif [[ $desc == *"process"* ]]; then
        echo "⚙️"
    elif [[ $desc == *"courier"* ]]; then
        echo "🚛"
    elif [[ $desc == *"delivered"* ]]; then
        echo "✅"
    elif [ $index -eq 0 ]; then
        echo "🏁"
    elif [ $index -eq $((total-1)) ]; then
        echo "🎯"
    else
        echo "📍"
    fi
}

# Function to parse shipper info
parse_shipper_info() {
    local pengirim="$1"
    if [[ $pengirim == *"WH "* ]]; then
        echo "👤 Name: WAREHOUSE"
        echo "📍 Address: $(echo "$pengirim" | sed 's/WH //')"
    else
        echo "👤 Name: $pengirim"
        echo "📍 Address: N/A"
    fi
}

# Function to parse receiver info
parse_receiver_info() {
    local tujuan="$1"
    local words=($tujuan)
    local name="${words[0]} ${words[1]}"
    local address="${words[@]:2}"
    
    echo "👤 Name: ${name:-N/A}"
    echo "📍 Address: ${address:-N/A}"
}

# Main function
track_resi() {
    print_color $BLUE "Getting tracking data..."
    
    # Step 1: Get CSRF tokens
    local csrf_response=$(curl -s "https://cekresi.com/?noresi=$RESI_NUMBER&e=$EXPEDITION" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    if [ -z "$csrf_response" ]; then
        print_color $RED "❌ Failed to connect to cekresi.com"
        exit 1
    fi
    
    local viewstate=$(extract_value "$csrf_response" "viewstate")
    local secret_key=$(extract_value "$csrf_response" "secret_key")
    
    if [ -z "$viewstate" ] || [ -z "$secret_key" ]; then
        print_color $RED "❌ Failed to get CSRF tokens"
        exit 1
    fi
    
    # Step 2: Get tracking data
    local timers="kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
    local post_data="viewstate=$viewstate&secret_key=$secret_key&e=$EXPEDITION&noresi=$RESI_NUMBER&timers=$timers"
    
    local tracking_response=$(curl -s "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f" \
        -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" \
        -H "Origin: https://cekresi.com" \
        -H "Referer: https://cekresi.com/" \
        -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
        --data-raw "$post_data")
    
    if [ -z "$tracking_response" ]; then
        print_color $RED "❌ Failed to get tracking data"
        exit 1
    fi
    
    # Check if tracking data exists
    if [[ $tracking_response == *"alert-success"* ]]; then
        print_color $GREEN "✅ Tracking data retrieved successfully!"
        echo ""
        display_tracking_info "$tracking_response"
    else
        print_color $RED "❌ No tracking data found"
        echo "📦 Tracking Number: $RESI_NUMBER"
        echo "🚚 Expedition: $EXPEDITION"
    fi
}

# Function to display tracking information
display_tracking_info() {
    local html="$1"
    
    print_color $CYAN "📦 SHIPPING TRACKING INFORMATION"
    echo "══════════════════════════════════════════════════"
    
    # Extract basic information
    local expedisi=$(echo "$html" | grep -o '<strong>[^<]*Express[^<]*</strong>' | head -1 | clean_text)
    local status=$(echo "$html" | grep -o 'id="status_resi"[^>]*>[^<]*' | sed 's/id="status_resi"[^>]*>//' | clean_text)
    local pengirim=$(echo "$html" | grep -A2 "Dikirim oleh" | tail -1 | clean_text)
    local tujuan=$(echo "$html" | grep -A2 "Dikirim ke" | tail -1 | clean_text)
    local tanggal_kirim=$(echo "$html" | grep -A2 "Dikirim tanggal" | tail -1 | clean_text)
    
    # Basic Information
    echo ""
    print_color $YELLOW "📋 BASIC INFORMATION:"
    echo "─────────────────────────"
    echo "📍 Tracking Number: $RESI_NUMBER"
    echo "📅 Shipment Date: ${tanggal_kirim:-N/A}"
    echo "🚚 Courier: ${expedisi:-N/A}"
    echo "📊 Status: $(get_status_icon "$status") ${status:-N/A}"
    
    # Shipper Information
    echo ""
    print_color $YELLOW "📤 SHIPPER INFORMATION:"
    echo "─────────────────────────"
    parse_shipper_info "$pengirim"
    
    # Receiver Information
    echo ""
    print_color $YELLOW "📥 RECEIVER INFORMATION:"
    echo "─────────────────────────"
    parse_receiver_info "$tujuan"
    
    # Tracking History
    echo ""
    print_color $YELLOW "🛣️  TRACKING HISTORY:"
    echo "─────────────────────────"
    
    # Extract tracking history (simplified)
    local history_section=$(echo "$html" | grep -A 50 "collapseTwo")
    local tracking_lines=$(echo "$history_section" | grep -o '<tr[^>]*>.*</tr>' | grep -v "Tanggal")
    
    local index=0
    local total=$(echo "$tracking_lines" | wc -l)
    
    while IFS= read -r line; do
        if [ ! -z "$line" ]; then
            local date=$(echo "$line" | grep -o '<td[^>]*>[^<]*</td>' | head -1 | clean_text)
            local desc=$(echo "$line" | grep -o '<td[^>]*>[^<]*</td>' | tail -1 | clean_text)
            
            if [ ! -z "$date" ] && [ ! -z "$desc" ] && [ "$date" != "Tanggal" ]; then
                local icon=$(get_tracking_icon "$desc" $index $total)
                echo "$icon $date - $desc"
                ((index++))
            fi
        fi
    done <<< "$tracking_lines"
    
    echo ""
    echo "══════════════════════════════════════════════════"
}

# Check if resi number is provided
if [ -z "$RESI_NUMBER" ]; then
    show_usage
    exit 1
fi

# Run tracking
track_resi
