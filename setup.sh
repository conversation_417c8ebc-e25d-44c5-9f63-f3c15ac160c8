#!/bin/bash

echo "📦 RESI TRACKER SETUP"
echo "=========================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed!"
    echo ""
    echo "💡 Please install Node.js from: https://nodejs.org/"
    echo "   Download the LTS version and install it."
    echo ""
    exit 1
fi

echo "✅ Node.js is installed"
node --version

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available!"
    exit 1
fi

echo "✅ npm is available"
npm --version
echo ""

# Install dependencies
echo "📥 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies!"
    exit 1
fi

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📋 Usage:"
echo "   node test-tracking.js <resi_number> [expedition]"
echo ""
echo "📝 Example:"
echo "   node test-tracking.js CSS0925755453307 JNE"
echo ""
echo "🚚 Available expeditions: JNE, JET, TIKI, POS, etc."
echo ""
