import ManualJS from "./manual.js";
import axios from "axios";
import * as cheerio from "cheerio";

const encryptTimers = (number) =>
  new Promise((resolve, reject) => {
    const result = ManualJS.MDX.goinstring(
      number,
      ManualJS.jun.Des.parse("79540e250fdb16afac03e19c46dbdeb3"),
      { ii: ManualJS.jun.Des.parse("eb2bb9425e81ffa942522e4414e95bd0") }
    ).rabbittext.toString(ManualJS.jun.Text21);
    resolve(encodeURIComponent(result));
  });

const getCSRFData = async (number, expedition = "JNE") => {
  const response = await axios.get(
    `https://cekresi.com/?noresi=${number}&e=${expedition}`,
    {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "en-US,en;q=0.9,id;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "sec-ch-ua": '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
      },
    }
  );

  const $ = cheerio.load(response.data);
  const viewstate = $('input[name="viewstate"]').val();
  const secret_key = $('input[name="secret_key"]').val();

  // Extract dynamic API URL parameters from the page
  let apiUrl = null;
  let apiParams = null;

  // Try to extract UI parameter from page content
  const pageContent = response.data;
  const uiMatch = pageContent.match(/ui=([a-f0-9]{32})/);
  const apaMatch = pageContent.match(/apa(\d+)\.cekresi\.com/);

  // Known working endpoints to try (most recent first)
  const knownEndpoints = [
    {
      url: "https://apa2.cekresi.com/cekresi/resi/initialize.php",
      params: { ui: "5c5941b4aea5c2e56c3d15738adee75a", p: "1", w: "vo73cp" }
    },
    {
      url: "https://apa1.cekresi.com/cekresi/resi/initialize.php",
      params: { ui: "49de28a9b44201dcb24c77bf19985a12", p: "1", w: "lyk55f" }
    }
  ];

  // If we found UI parameter in page, try to use it with detected subdomain
  if (uiMatch && apaMatch) {
    apiUrl = `https://apa${apaMatch[1]}.cekresi.com/cekresi/resi/initialize.php`;
    apiParams = {
      ui: uiMatch[1],
      p: "1",
      w: "vo73cp" // Default w parameter, might need updating
    };
    console.log(`Extracted from page: UI=${uiMatch[1]}, Subdomain=apa${apaMatch[1]}`);
  } else {
    // Use the most recent known working endpoint
    const endpoint = knownEndpoints[0];
    apiUrl = endpoint.url;
    apiParams = endpoint.params;
    console.log("Using known working endpoint (fallback)");
  }

  return {
    viewstate,
    secret_key,
    apiUrl,
    apiParams
  };
};

const scrapeResiData = (html) => {
  const $ = cheerio.load(html);
  const expedisi = $(".alert.alert-success strong").eq(1).text();
  const noResi = $(".alert.alert-success strong").eq(2).text();
  const pengirim = $('td:contains("Dikirim oleh")').next().next().text().trim();
  const tujuan = $('td:contains("Dikirim ke")')
    .next()
    .next()
    .text()
    .replace(/\s+/g, " ")
    .trim();
  const status = $("#status_resi").text().trim();
  const tanggalKirim = $('td:contains("Dikirim tanggal")')
    .next()
    .next()
    .text()
    .trim();
  const penerima = $("#last_position").text().trim();

  const perjalanan = [];
  $("#collapseTwo .table tr").each((_, row) => {
    const tanggal = $(row).find("td").eq(0).text().trim();
    const keterangan = $(row).find("td").eq(1).text().trim();
    if (tanggal && keterangan && tanggal !== "Tanggal") {
      perjalanan.push({ tanggal, keterangan });
    }
  });

  return {
    expedisi,
    noResi,
    pengirim,
    tujuan,
    status,
    tanggalKirim,
    penerima,
    perjalanan,
  };
};

const getResiTracking = (number, csrf, timers, expedition = "JNE") =>
  new Promise(async (resolve, reject) => {
    try {
      const postData = `viewstate=${csrf.viewstate}&secret_key=${csrf.secret_key}&e=${expedition}&noresi=${number}&timers=${timers}`;

      // List of endpoints to try (most recent first)
      const endpointsToTry = [
        {
          url: `${csrf.apiUrl}?ui=${csrf.apiParams.ui}&p=${csrf.apiParams.p}&w=${csrf.apiParams.w}`,
          name: "Extracted/Primary"
        },
        {
          url: "https://apa2.cekresi.com/cekresi/resi/initialize.php?ui=5c5941b4aea5c2e56c3d15738adee75a&p=1&w=vo73cp",
          name: "Known Working (apa2)"
        },
        {
          url: "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f",
          name: "Fallback (apa1)"
        }
      ];

      let lastError = null;
      let getResponse = null;

      // Try each endpoint until one works
      for (const endpoint of endpointsToTry) {
        try {
          console.log(`Trying ${endpoint.name}: ${endpoint.url}`);

          getResponse = await axios.post(
            endpoint.url,
            postData,
            {
              method: "POST",
              headers: {
                Accept: "*/*",
                "Accept-Language": "en-US,en;q=0.9,id;q=0.8",
                Connection: "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                Origin: "https://cekresi.com",
                Referer: "https://cekresi.com/",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-site",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "sec-ch-ua": '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
              },
              timeout: 15000 // 15 second timeout per attempt
            }
          );

          // Check if response contains tracking data
          if (getResponse.data && (getResponse.data.includes('alert-success') || getResponse.data.includes('tracking'))) {
            console.log(`✅ Success with ${endpoint.name}`);
            break; // Exit loop with successful response
          } else {
            console.log(`❌ No tracking data from ${endpoint.name}`);
            getResponse = null; // Reset for next attempt
            continue;
          }

        } catch (error) {
          console.log(`❌ Error with ${endpoint.name}: ${error.message}`);
          lastError = error;
          getResponse = null; // Reset for next attempt
          continue;
        }
      }

      // If we get here without a successful response, throw error
      if (!getResponse) {
        throw lastError || new Error("All endpoints failed");
      }
      const resiData = scrapeResiData(getResponse.data);

      if (resiData) {
        resolve({
          valid: true,
          data: resiData,
        });
      } else {
        resolve({
          valid: false,
          message: "Data tidak valid atau tidak ditemukan",
        });
      }
    } catch (err) {
      reject({ valid: false, error: err.message });
    }
  });

export { getCSRFData, encryptTimers, getResiTracking };
