#!/usr/bin/env python3
"""
Resi Tracking Tool - Python Version
Usage: python track_resi.py <resi_number> [expedition]
Example: python track_resi.py CSS0925755453307 JNE
"""

import sys
import requests
import re
from bs4 import BeautifulSoup
import urllib.parse
import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import json

class ResiTracker:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        })
    
    def get_csrf_data(self, resi_number, expedition="JNE"):
        """Get CSRF tokens from cekresi.com"""
        url = f"https://cekresi.com/?noresi={resi_number}&e={expedition}"
        
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,id;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
        }
        
        response = self.session.get(url, headers=headers)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        viewstate_input = soup.find('input', {'name': 'viewstate'})
        secret_key_input = soup.find('input', {'name': 'secret_key'})
        
        return {
            'viewstate': viewstate_input['value'] if viewstate_input else None,
            'secret_key': secret_key_input['value'] if secret_key_input else None
        }
    
    def encrypt_timers(self, resi_number):
        """Create encrypted timers (simplified version using known working value)"""
        # For simplicity, we'll use the known working encrypted value
        # In the original JS, this uses a complex encryption algorithm
        return "kRNxDiSZ%2BCLoe%2BuFrnFrwLtDxJwvxH4%2BvJdPUhIEBys%3D"
    
    def get_tracking_data(self, resi_number, csrf_data, timers, expedition="JNE"):
        """Get tracking data from the API"""
        url = "https://apa1.cekresi.com/cekresi/resi/initialize.php?ui=49de28a9b44201dcb24c77bf19985a12&p=1&w=lyk55f"
        
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,id;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://cekresi.com',
            'Referer': 'https://cekresi.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'sec-ch-ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        
        data = {
            'viewstate': csrf_data['viewstate'],
            'secret_key': csrf_data['secret_key'],
            'e': expedition,
            'noresi': resi_number,
            'timers': timers
        }
        
        response = self.session.post(url, headers=headers, data=data)
        return response.text
    
    def parse_tracking_data(self, html):
        """Parse tracking data from HTML response"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Check if tracking data exists
        if not soup.find(class_='alert-success'):
            return None
        
        # Extract basic information
        expedisi = ""
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if 'Express' in tag.get_text():
                expedisi = tag.get_text().strip()
                break
        
        status_elem = soup.find(id='status_resi')
        status = status_elem.get_text().strip() if status_elem else ""
        
        # Extract shipper and receiver info
        pengirim = ""
        tujuan = ""
        tanggal_kirim = ""
        
        # Find table rows with shipping info
        rows = soup.find_all('tr')
        for row in rows:
            cells = row.find_all('td')
            if len(cells) >= 3:
                label = cells[0].get_text().strip()
                value = cells[2].get_text().strip()
                
                if 'Dikirim oleh' in label:
                    pengirim = value
                elif 'Dikirim ke' in label:
                    tujuan = value
                elif 'Dikirim tanggal' in label:
                    tanggal_kirim = value
        
        # Extract tracking history
        perjalanan = []
        tracking_table = soup.find(id='collapseTwo')
        if tracking_table:
            tracking_rows = tracking_table.find_all('tr')
            for row in tracking_rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    tanggal = cells[0].get_text().strip()
                    keterangan = cells[1].get_text().strip()
                    if tanggal and keterangan and tanggal != 'Tanggal':
                        perjalanan.append({
                            'tanggal': tanggal,
                            'keterangan': keterangan
                        })
        
        return {
            'expedisi': expedisi,
            'status': status,
            'pengirim': pengirim,
            'tujuan': tujuan,
            'tanggalKirim': tanggal_kirim,
            'perjalanan': perjalanan
        }
    
    def get_status_icon(self, status):
        """Get appropriate icon for status"""
        status_lower = status.lower()
        if 'delivered' in status_lower:
            return "✅"
        elif 'transit' in status_lower or 'process' in status_lower:
            return "🚛"
        elif 'picked' in status_lower or 'received' in status_lower:
            return "📦"
        elif 'pending' in status_lower or 'waiting' in status_lower:
            return "⏳"
        elif 'failed' in status_lower or 'error' in status_lower:
            return "❌"
        else:
            return "📋"
    
    def parse_shipper_info(self, pengirim):
        """Parse shipper information"""
        if not pengirim:
            return {'name': 'N/A', 'address': 'N/A'}
        
        clean = re.sub(r'\s+', ' ', pengirim).strip()
        
        if 'WH ' in clean:
            return {
                'name': 'WAREHOUSE',
                'address': clean.replace('WH ', '')
            }
        
        return {
            'name': clean if clean else 'N/A',
            'address': 'N/A'
        }
    
    def parse_receiver_info(self, tujuan):
        """Parse receiver information"""
        if not tujuan:
            return {'name': 'N/A', 'address': 'N/A'}
        
        clean = re.sub(r'\s+', ' ', tujuan).strip()
        words = clean.split()
        
        return {
            'name': ' '.join(words[:2]) if len(words) >= 2 else 'N/A',
            'address': ' '.join(words[2:]) if len(words) > 2 else 'N/A'
        }
    
    def get_tracking_icon(self, description, index, total):
        """Get appropriate icon for tracking steps"""
        desc = description.lower()
        
        if 'picked up' in desc or 'shipment picked' in desc:
            return '📦'
        elif 'received at' in desc and 'sorting' in desc:
            return '🏭'
        elif 'received at' in desc and 'sorting' not in desc:
            return '📍'
        elif 'processed at' in desc or 'process and forward' in desc:
            return '⚙️'
        elif 'with delivery courier' in desc or 'out for delivery' in desc:
            return '🚛'
        elif 'delivered to' in desc:
            return '✅'
        elif 'transit' in desc or 'forward' in desc:
            return '🚚'
        elif index == 0:
            return '🏁'  # Start
        elif index == total - 1:
            return '🎯'  # End
        else:
            return '📍'  # Default
    
    def display_tracking_info(self, data, resi_number):
        """Display formatted tracking information"""
        print("📦 SHIPPING TRACKING INFORMATION")
        print("═" * 50)
        
        # Basic Information
        print("\n📋 BASIC INFORMATION:")
        print("─" * 25)
        print(f"📍 Tracking Number: {resi_number}")
        print(f"📅 Shipment Date: {data.get('tanggalKirim', 'N/A')}")
        print(f"🚚 Courier: {data.get('expedisi', 'N/A')}")
        print(f"📊 Status: {self.get_status_icon(data.get('status', ''))} {data.get('status', 'N/A')}")
        
        # Shipper Information
        print("\n📤 SHIPPER INFORMATION:")
        print("─" * 25)
        shipper_info = self.parse_shipper_info(data.get('pengirim', ''))
        print(f"👤 Name: {shipper_info['name']}")
        print(f"📍 Address: {shipper_info['address']}")
        
        # Receiver Information
        print("\n📥 RECEIVER INFORMATION:")
        print("─" * 25)
        receiver_info = self.parse_receiver_info(data.get('tujuan', ''))
        print(f"👤 Name: {receiver_info['name']}")
        print(f"📍 Address: {receiver_info['address']}")
        
        # Tracking History
        perjalanan = data.get('perjalanan', [])
        if perjalanan:
            print("\n🛣️  TRACKING HISTORY:")
            print("─" * 25)
            for index, item in enumerate(perjalanan):
                icon = self.get_tracking_icon(item['keterangan'], index, len(perjalanan))
                print(f"{icon} {item['tanggal']} - {item['keterangan']}")
        
        print("\n" + "═" * 50)
    
    def track(self, resi_number, expedition="JNE"):
        """Main tracking function"""
        try:
            print("Getting tracking data...")
            
            # Get CSRF tokens
            csrf_data = self.get_csrf_data(resi_number, expedition)
            
            if not csrf_data['viewstate'] or not csrf_data['secret_key']:
                raise Exception('Failed to get CSRF tokens')
            
            # Get encrypted timers
            timers = self.encrypt_timers(resi_number)
            
            # Get tracking data
            html_response = self.get_tracking_data(resi_number, csrf_data, timers, expedition)
            
            # Parse tracking data
            tracking_data = self.parse_tracking_data(html_response)
            
            if tracking_data:
                print("✅ Tracking data retrieved successfully!")
                print("")
                self.display_tracking_info(tracking_data, resi_number)
            else:
                print("❌ No tracking data found")
                print(f"📦 Tracking Number: {resi_number}")
                print(f"🚚 Expedition: {expedition}")
                
        except Exception as error:
            print("❌ ERROR OCCURRED")
            print(f"📋 Error: {str(error)}")

def main():
    if len(sys.argv) < 2:
        print("📦 RESI TRACKING TOOL")
        print("═" * 30)
        print("❌ Please provide a tracking number")
        print("\n📋 Usage:")
        print("   python track_resi.py <tracking_number> [expedition]")
        print("\n📝 Examples:")
        print("   python track_resi.py CSS0925755453307 JNE")
        print("   python track_resi.py JET123456789 JET")
        print("   python track_resi.py TIKI123456789 TIKI")
        print("\n🚚 Available expeditions:")
        print("   JNE, JET, TIKI, POS, SICEPAT, ANTERAJA, etc.")
        sys.exit(1)
    
    resi_number = sys.argv[1]
    expedition = sys.argv[2] if len(sys.argv) > 2 else "JNE"
    
    tracker = ResiTracker()
    tracker.track(resi_number, expedition)

if __name__ == "__main__":
    main()
