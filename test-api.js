import axios from 'axios';

// Test function to call the API endpoint
async function testAPI(resiNumber, baseUrl = 'http://localhost:3000') {
  console.log(`🌐 Testing API endpoint: ${baseUrl}/cek-resi/${resiNumber}`);
  console.log("=" .repeat(60));
  
  try {
    const response = await axios.get(`${baseUrl}/cek-resi/${resiNumber}`, {
      timeout: 30000, // 30 second timeout
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log("✅ API Response received!");
    console.log(`Status Code: ${response.status}`);
    console.log("Response Data:");
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    if (error.response) {
      console.error("❌ API Error Response:");
      console.error(`Status Code: ${error.response.status}`);
      console.error("Response Data:", error.response.data);
    } else if (error.request) {
      console.error("❌ Network Error:");
      console.error("No response received from server");
      console.error("Make sure the server is running on", baseUrl);
    } else {
      console.error("❌ Request Error:");
      console.error(error.message);
    }
  }
}

// Test server health
async function testServerHealth(baseUrl = 'http://localhost:3000') {
  console.log(`🏥 Testing server health: ${baseUrl}`);
  console.log("=" .repeat(40));
  
  try {
    const response = await axios.get(baseUrl, { timeout: 5000 });
    console.log("✅ Server is running!");
    console.log("Response:", response.data);
    return true;
  } catch (error) {
    console.error("❌ Server is not responding");
    console.error("Make sure to start the server with: npm start");
    return false;
  }
}

// Main execution
async function main() {
  const resiNumber = process.argv[2];
  const baseUrl = process.argv[3] || 'http://localhost:3000';
  
  if (!resiNumber) {
    console.log("❌ Please provide a resi number as argument");
    console.log("Usage: node test-api.js <resi_number> [base_url]");
    console.log("Example: node test-api.js JET123456789");
    console.log("Example: node test-api.js JET123456789 http://localhost:3000");
    process.exit(1);
  }
  
  // First test server health
  const serverRunning = await testServerHealth(baseUrl);
  
  if (serverRunning) {
    console.log("\n");
    await testAPI(resiNumber, baseUrl);
  }
}

main();
