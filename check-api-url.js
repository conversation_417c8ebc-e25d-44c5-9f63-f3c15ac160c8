#!/usr/bin/env node

// Script to check and extract current API URL parameters from cekresi.com
import axios from 'axios';
import * as cheerio from 'cheerio';

async function checkCurrentApiUrl() {
  console.log('🔍 Checking current API URL parameters...');
  console.log('=' .repeat(50));
  
  try {
    const response = await axios.get('https://cekresi.com/?noresi=TEST123&e=JNE', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,id;q=0.8',
      }
    });
    
    const $ = cheerio.load(response.data);
    
    console.log('📋 Searching for API URL patterns...');
    
    let foundUrls = [];
    
    // Method 1: Search in script tags
    $('script').each((_, script) => {
      const scriptContent = $(script).html();
      if (scriptContent) {
        // Look for apa*.cekresi.com URLs
        const urlMatches = scriptContent.match(/apa\d+\.cekresi\.com\/cekresi\/resi\/initialize\.php\?ui=([^&"'\s]+)&p=(\d+)&w=([^"'\s&]+)/g);
        if (urlMatches) {
          urlMatches.forEach(url => {
            if (!foundUrls.includes(url)) {
              foundUrls.push(url);
            }
          });
        }
      }
    });
    
    // Method 2: Search in form actions
    $('form').each((_, form) => {
      const action = $(form).attr('action');
      if (action && action.includes('apa') && action.includes('cekresi.com')) {
        if (!foundUrls.includes(action)) {
          foundUrls.push(action);
        }
      }
    });
    
    // Method 3: Search in all text content for URL patterns
    const pageText = response.data;
    const allUrlMatches = pageText.match(/https?:\/\/apa\d+\.cekresi\.com\/cekresi\/resi\/initialize\.php\?ui=([^&"'\s]+)&p=(\d+)&w=([^"'\s&]+)/g);
    if (allUrlMatches) {
      allUrlMatches.forEach(url => {
        if (!foundUrls.includes(url)) {
          foundUrls.push(url);
        }
      });
    }

    // Method 4: Search for any apa*.cekresi.com references
    const apaMatches = pageText.match(/apa\d+\.cekresi\.com[^"'\s]*/g);
    if (apaMatches) {
      console.log('\n🔍 Found apa*.cekresi.com references:');
      apaMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
      });
    }

    // Method 5: Search for initialize.php references
    const initMatches = pageText.match(/[^"'\s]*initialize\.php[^"'\s]*/g);
    if (initMatches) {
      console.log('\n🔍 Found initialize.php references:');
      initMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
      });
    }

    // Method 6: Search for ui= parameters
    const uiMatches = pageText.match(/ui=([a-f0-9]+)/g);
    if (uiMatches) {
      console.log('\n🔍 Found ui= parameters:');
      uiMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
      });
    }

    // Method 7: Search for w= parameters
    const wMatches = pageText.match(/w=([a-z0-9]+)/g);
    if (wMatches) {
      console.log('\n🔍 Found w= parameters:');
      wMatches.forEach((match, index) => {
        console.log(`   ${index + 1}. ${match}`);
      });
    }

    // Method 8: Try to reconstruct the URL from parts
    const uiMatch = pageText.match(/ui=([a-f0-9]+)/);
    const wMatch = pageText.match(/w=([a-z0-9]+)/);
    const apaMatch = pageText.match(/apa(\d+)\.cekresi\.com/);

    if (uiMatch && wMatch && apaMatch) {
      const reconstructedUrl = `https://apa${apaMatch[1]}.cekresi.com/cekresi/resi/initialize.php?ui=${uiMatch[1]}&p=1&w=${wMatch[1]}`;
      console.log('\n🔧 Reconstructed URL:');
      console.log(`   ${reconstructedUrl}`);

      console.log('\n📝 Parameters:');
      console.log(`   Subdomain: apa${apaMatch[1]}`);
      console.log(`   UI: ${uiMatch[1]}`);
      console.log(`   P: 1`);
      console.log(`   W: ${wMatch[1]}`);
    }
    
    console.log(`\n📦 Found ${foundUrls.length} API URL(s):`);
    
    if (foundUrls.length > 0) {
      foundUrls.forEach((url, index) => {
        console.log(`\n${index + 1}. ${url}`);
        
        // Parse URL parameters
        const urlMatch = url.match(/apa(\d+)\.cekresi\.com\/cekresi\/resi\/initialize\.php\?ui=([^&]+)&p=(\d+)&w=([^&\s"']+)/);
        if (urlMatch) {
          console.log(`   📍 Subdomain: apa${urlMatch[1]}`);
          console.log(`   🔑 UI: ${urlMatch[2]}`);
          console.log(`   📊 P: ${urlMatch[3]}`);
          console.log(`   🔧 W: ${urlMatch[4]}`);
          
          // Generate code for function.js
          console.log(`\n   📝 Code for function.js:`);
          console.log(`   apiUrl = "https://apa${urlMatch[1]}.cekresi.com/cekresi/resi/initialize.php";`);
          console.log(`   apiParams = { ui: "${urlMatch[2]}", p: "${urlMatch[3]}", w: "${urlMatch[4]}" };`);
        }
      });
      
      // Show the most recent/likely URL
      const latestUrl = foundUrls[foundUrls.length - 1];
      console.log(`\n✅ Latest/Current URL: ${latestUrl}`);
      
    } else {
      console.log('❌ No API URLs found in the page');
      console.log('\n🔍 Debugging info:');
      console.log(`Page length: ${response.data.length} characters`);
      console.log(`Contains "apa": ${response.data.includes('apa')}`);
      console.log(`Contains "cekresi": ${response.data.includes('cekresi')}`);
      console.log(`Contains "initialize": ${response.data.includes('initialize')}`);
      
      // Show first 1000 characters for manual inspection
      console.log('\n📄 First 1000 characters of page:');
      console.log(response.data.substring(0, 1000));
    }
    
    // Check CSRF tokens too
    const viewstate = $('input[name="viewstate"]').val();
    const secret_key = $('input[name="secret_key"]').val();
    
    console.log(`\n🔐 CSRF Tokens:`);
    console.log(`   Viewstate: ${viewstate ? 'Found' : 'Not found'}`);
    console.log(`   Secret Key: ${secret_key ? 'Found' : 'Not found'}`);
    
  } catch (error) {
    console.error('❌ Error checking API URL:', error.message);
  }
}

// Run the check
checkCurrentApiUrl();
