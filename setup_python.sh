#!/bin/bash

echo "🐍 PYTHON RESI TRACKER SETUP"
echo "=========================================="
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python is not installed!"
        echo ""
        echo "💡 Please install Python from: https://python.org/"
        echo "   Or use your package manager:"
        echo "   Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "   CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "   macOS: brew install python3"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python is installed"
$PYTHON_CMD --version

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ pip is not available!"
        echo ""
        echo "💡 Please install pip:"
        echo "   Ubuntu/Debian: sudo apt install python3-pip"
        echo "   CentOS/RHEL: sudo yum install python3-pip"
        echo ""
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo "✅ pip is available"
$PIP_CMD --version
echo ""

# Install dependencies
echo "📥 Installing Python dependencies..."
$PIP_CMD install -r requirements_simple.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies!"
    echo ""
    echo "💡 Try installing manually:"
    echo "   $PIP_CMD install --user requests beautifulsoup4"
    echo ""
    exit 1
fi

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📋 Usage:"
echo "   $PYTHON_CMD track_resi_simple.py <resi_number> [expedition]"
echo ""
echo "📝 Example:"
echo "   $PYTHON_CMD track_resi_simple.py CSS0925755453307 JNE"
echo ""
echo "🚚 Available expeditions: JNE, JET, TIKI, POS, etc."
echo ""
