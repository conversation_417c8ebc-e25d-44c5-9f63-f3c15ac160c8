#!/usr/bin/env python3
"""
Bot Functions for Resi Tracking
Fixed versions that work with subprocess.run()
"""

import subprocess
import os

def cek_resi_fixed(no_resi, expedition="JNE"):
    """
    Fixed version using proper subprocess command structure
    """
    try:
        # Use absolute paths and separate command from arguments
        script_dir = "/home/<USER>/bot/cek-resi"
        node_path = "/usr/bin/node"
        script_file = os.path.join(script_dir, "test-tracking.js")
        
        # Construct the command as a list
        command = [node_path, script_file, no_resi, expedition]
        
        # Run the command with proper working directory
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            check=True,
            cwd=script_dir,  # Important: set working directory
            timeout=60  # Add timeout to prevent hanging
        )
        
        return result.stdout
        
    except subprocess.CalledProcessError as e:
        return f"Command execution error: {e.stderr if e.stderr else str(e)}"
    except subprocess.TimeoutExpired:
        return "Error: Request timed out after 60 seconds"
    except FileNotFoundError:
        return "Error: Node.js or script file not found. Check paths."
    except Exception as e:
        return f"Unexpected error: {str(e)}"

def cek_resi_with_shell(no_resi, expedition="JNE"):
    """
    Alternative version using shell=True
    """
    try:
        script_dir = "/home/<USER>/bot/cek-resi"
        
        # Use shell command with cd
        command = f"cd {script_dir} && /usr/bin/node test-tracking.js {no_resi} {expedition}"
        
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True,
            timeout=60
        )
        
        return result.stdout
        
    except subprocess.CalledProcessError as e:
        return f"Command execution error: {e.stderr if e.stderr else str(e)}"
    except subprocess.TimeoutExpired:
        return "Error: Request timed out after 60 seconds"
    except Exception as e:
        return f"Unexpected error: {str(e)}"

def cek_resi_with_wrapper(no_resi, expedition="JNE"):
    """
    Version using the wrapper script (if you create it)
    """
    try:
        wrapper_script = "/home/<USER>/bot/cek-resi/track-resi-wrapper.js"
        
        command = ["/usr/bin/node", wrapper_script, no_resi, expedition]
        
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            timeout=60
        )
        
        return result.stdout
        
    except subprocess.CalledProcessError as e:
        return f"Command execution error: {e.stderr if e.stderr else str(e)}"
    except subprocess.TimeoutExpired:
        return "Error: Request timed out after 60 seconds"
    except FileNotFoundError:
        return "Error: Wrapper script not found"
    except Exception as e:
        return f"Unexpected error: {str(e)}"

def cek_resi_bash_wrapper(no_resi, expedition="JNE"):
    """
    Version using bash wrapper script
    """
    try:
        script_path = "/home/<USER>/bot/cek-resi/track-resi-bot.sh"
        
        # Make sure script is executable
        os.chmod(script_path, 0o755)
        
        command = [script_path, no_resi, expedition]
        
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            timeout=60
        )
        
        return result.stdout
        
    except subprocess.CalledProcessError as e:
        return f"Command execution error: {e.stderr if e.stderr else str(e)}"
    except subprocess.TimeoutExpired:
        return "Error: Request timed out after 60 seconds"
    except FileNotFoundError:
        return "Error: Bash wrapper script not found"
    except Exception as e:
        return f"Unexpected error: {str(e)}"

# Test functions
def test_all_methods():
    """Test all methods with a sample resi number"""
    test_resi = "CSS0925755453307"
    
    print("🧪 Testing all resi tracking methods...")
    print("=" * 50)
    
    methods = [
        ("Fixed Node.js", cek_resi_fixed),
        ("Shell Command", cek_resi_with_shell),
        ("Wrapper Script", cek_resi_with_wrapper),
        ("Bash Wrapper", cek_resi_bash_wrapper)
    ]
    
    for name, method in methods:
        print(f"\n📋 Testing {name}:")
        try:
            result = method(test_resi, "JNE")
            if "Error" in result or "error" in result:
                print(f"❌ {result}")
            else:
                print(f"✅ Success! Output length: {len(result)} characters")
                # Show first few lines
                lines = result.split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    test_all_methods()
